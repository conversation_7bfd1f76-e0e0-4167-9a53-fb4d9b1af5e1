import axios from "axios";
import { addIptContextToPath, getAuthHeadersWithIptContext } from "@/lib/ipt-context";

export class Student {
    public static api_url = process.env.NEXT_PUBLIC_API_URL;

    public static async index() {
        try {
          const url = addIptContextToPath(`${this.api_url}/other/getStudents`);
          const response = await axios.get(url, {
            headers: getAuthHeadersWithIptContext()
          });

          if (response.status === 200) {
            return response.data;
          }
        } catch (error: any) {
          throw error.response.data;
        }
      }

    public static async show(id: string) {
        try {
            const url = addIptContextToPath(`${this.api_url}/other/getStudent/${id}`);
            const response = await axios.get(url, {
              headers: getAuthHeadersWithIptContext()
            });

            if (response.status === 200) {
                return response.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }
}