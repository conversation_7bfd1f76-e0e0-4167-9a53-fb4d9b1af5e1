"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useAddDailyLogFeedback, useAddFinalReportFeedback } from "../api/use-supervisor-feedback"
import { Loader2, MessageSquare } from "lucide-react"

interface FeedbackModalProps {
  isOpen: boolean
  onClose: () => void
  type: "daily_log" | "final_report"
  itemId: string
  studentName: string
  title: string
}

export const FeedbackModal = ({ 
  isOpen, 
  onClose, 
  type, 
  itemId, 
  studentName, 
  title 
}: FeedbackModalProps) => {
  const [feedback, setFeedback] = useState("")
  const [rating, setRating] = useState<string>("")
  const [grade, setGrade] = useState("")

  const dailyLogMutation = useAddDailyLogFeedback()
  const finalReportMutation = useAddFinalReportFeedback()

  const isLoading = dailyLogMutation.isPending || finalReportMutation.isPending

  const handleSubmit = async () => {
    if (!feedback.trim()) {
      return
    }

    try {
      if (type === "daily_log") {
        await dailyLogMutation.mutateAsync({
          daily_log_id: itemId,
          supervisor_feedback: feedback
        })
      } else {
        await finalReportMutation.mutateAsync({
          final_report_id: itemId,
          feedback: {
            supervisor_comments: feedback,
            supervisor_rating: rating ? parseInt(rating) : undefined,
            grade: grade || undefined
          }
        })
      }
      
      // Reset form and close modal
      setFeedback("")
      setRating("")
      setGrade("")
      onClose()
    } catch (error) {
      // Error is handled by the mutation
      console.error("Failed to submit feedback:", error)
    }
  }

  const handleClose = () => {
    if (!isLoading) {
      setFeedback("")
      setRating("")
      setGrade("")
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Add Feedback
          </DialogTitle>
          <DialogDescription>
            Provide feedback for {studentName}'s {type === "daily_log" ? "daily log" : "final report"}: 
            <span className="font-medium"> {title}</span>
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Feedback text */}
          <div className="space-y-2">
            <Label htmlFor="feedback">
              {type === "daily_log" ? "Feedback Comments" : "Report Comments"}
            </Label>
            <Textarea
              id="feedback"
              placeholder={`Provide your feedback on this ${type === "daily_log" ? "daily log entry" : "final report"}...`}
              value={feedback}
              onChange={(e) => setFeedback(e.target.value)}
              className="min-h-[100px]"
              disabled={isLoading}
            />
          </div>

          {/* Final report specific fields */}
          {type === "final_report" && (
            <>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="rating">Rating (1-10)</Label>
                  <Select value={rating} onValueChange={setRating} disabled={isLoading}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select rating" />
                    </SelectTrigger>
                    <SelectContent>
                      {Array.from({ length: 10 }, (_, i) => i + 1).map((num) => (
                        <SelectItem key={num} value={num.toString()}>
                          {num} / 10
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="grade">Grade</Label>
                  <Select value={grade} onValueChange={setGrade} disabled={isLoading}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select grade" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="A+">A+</SelectItem>
                      <SelectItem value="A">A</SelectItem>
                      <SelectItem value="B+">B+</SelectItem>
                      <SelectItem value="B">B</SelectItem>
                      <SelectItem value="C+">C+</SelectItem>
                      <SelectItem value="C">C</SelectItem>
                      <SelectItem value="D+">D+</SelectItem>
                      <SelectItem value="D">D</SelectItem>
                      <SelectItem value="F">F</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!feedback.trim() || isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Submit Feedback
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
