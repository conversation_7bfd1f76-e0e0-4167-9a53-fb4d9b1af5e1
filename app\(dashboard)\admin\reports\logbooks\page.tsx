'use client'

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Download,
  Search,
  Filter,
  BookOpen,
  Calendar,
  User,
  Clock,
  CheckCircle2,
  AlertCircle,
  Eye,
  FileText,
  BarChart3
} from "lucide-react"
import { IPTLogbooks } from "@/features/logbook/server/IPTLogbooks"
import { toast } from "sonner"

interface StudentLogbookSummary {
  student_id: number
  student_name: string
  email: string
  total_daily_logs: number
  submitted_daily_logs: number
  total_weekly_reports: number
  submitted_weekly_reports: number
  current_week: number
  total_hours: number
  last_activity: string
  completion_rate: number
}

export default function AdminLogbooksPage() {
  const [loading, setLoading] = useState(true)
  const [students, setStudents] = useState<StudentLogbookSummary[]>([])
  const [selectedStudent, setSelectedStudent] = useState<StudentLogbookSummary | null>(null)
  const [studentDailyLogs, setStudentDailyLogs] = useState<any[]>([])
  const [studentWeeklyReports, setStudentWeeklyReports] = useState<any[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [filters, setFilters] = useState({
    student_name: '',
    status: 'all'
  })
  const [activeTab, setActiveTab] = useState('students')
  const [studentDetailTab, setStudentDetailTab] = useState('daily-logs')

  useEffect(() => {
    if (activeTab === 'students') {
      loadStudents()
    }
    loadStatistics()
  }, [filters, activeTab])

  const loadStudents = async () => {
    try {
      setLoading(true)
      const result = await IPTLogbooks.getStudentLogbookSummaries(filters)
      setStudents(result?.data || [])
    } catch (error) {
      console.error("Failed to load students:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadStatistics = async () => {
    try {
      const statsResult = await IPTLogbooks.getLogbookStatistics()
      setStatistics(statsResult?.data || null)
    } catch (error) {
      console.error("Failed to load statistics:", error)
    }
  }

  const loadStudentDetails = async (student: StudentLogbookSummary) => {
    try {
      setLoading(true)
      setSelectedStudent(student)
      setActiveTab('student-details')

      // Load student's daily logs
      const dailyLogsResult = await IPTLogbooks.getAllDailyLogs({
        student_id: student.student_id.toString()
      })
      setStudentDailyLogs(dailyLogsResult?.data || [])

      // Load student's weekly reports
      const weeklyReportsResult = await IPTLogbooks.getAllWeeklyReports({
        student_id: student.student_id.toString()
      })
      setStudentWeeklyReports(weeklyReportsResult?.data || [])

    } catch (error) {
      console.error("Failed to load student details:", error)
    } finally {
      setLoading(false)
    }
  }

  const handleExport = async () => {
    try {
      // This would typically generate and download a CSV/Excel file
      toast.info("Export functionality to be implemented")
    } catch (error) {
      console.error("Failed to export logbooks:", error)
    }
  }

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value === 'all' ? '' : value
    }))
  }

  const handleBackToStudents = () => {
    setActiveTab('students')
    setSelectedStudent(null)
    setStudentDailyLogs([])
    setStudentWeeklyReports([])
  }

  const getCompletionRate = () => {
    if (!statistics || statistics.total_daily_logs === 0) return 0
    return Math.round((statistics.submitted_daily_logs / statistics.total_daily_logs) * 100)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                {activeTab === 'students' ? (
                  <>
                    <h1 className="text-2xl font-bold">Student Logbook Management</h1>
                    <p className="text-muted-foreground">
                      View and manage logbook entries for all students
                    </p>
                  </>
                ) : (
                  <>
                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={handleBackToStudents}
                        className="flex items-center gap-2"
                      >
                        ← Back to Students
                      </Button>
                    </div>
                    <h1 className="text-2xl font-bold">{selectedStudent?.student_name}'s Logbook</h1>
                    <p className="text-muted-foreground">
                      Detailed view of daily logs and weekly reports for {selectedStudent?.email}
                    </p>
                  </>
                )}
              </div>
              <div className="flex items-center gap-2">
                <Button variant="outline" onClick={handleExport} className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  Export Data
                </Button>
              </div>
            </div>

            {/* Statistics Overview */}
            {statistics && (
              <div className="grid gap-4 md:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                    <User className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.total_students || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Registered students
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Daily Logs</CardTitle>
                    <BookOpen className="h-4 w-4 text-blue-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.submitted_daily_logs || 0}/{statistics.total_daily_logs || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      {getCompletionRate()}% submitted
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Weekly Reports</CardTitle>
                    <FileText className="h-4 w-4 text-green-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{statistics.submitted_weekly_reports || 0}/{statistics.total_weekly_reports || 0}</div>
                    <p className="text-xs text-muted-foreground">
                      Reports submitted
                    </p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Avg. Hours/Day</CardTitle>
                    <Clock className="h-4 w-4 text-orange-600" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {typeof statistics.average_hours_per_day === 'number'
                        ? statistics.average_hours_per_day.toFixed(1)
                        : '0.0'}
                    </div>
                    <p className="text-xs text-muted-foreground">
                      Current week: {statistics.current_week || 1}
                    </p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Filters */}
            {activeTab === 'students' && (
              <Card>
                <CardContent className="p-4">
                  <div className="flex flex-col md:flex-row gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search by student name..."
                          value={filters.student_name || ''}
                          onChange={(e) => handleFilterChange('student_name', e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>

                    <Select
                      value={filters.status || 'all'}
                      onValueChange={(value) => handleFilterChange('status', value)}
                    >
                      <SelectTrigger className="w-full md:w-40">
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Students</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Main Content Area */}
            {activeTab === 'students' ? (
              /* Students List */
              <Card>
                <CardHeader>
                  <CardTitle>Students with Logbook Entries</CardTitle>
                  <CardDescription>Click on a student to view their detailed logbook entries</CardDescription>
                </CardHeader>
                <CardContent>
                  {loading ? (
                    <div className="flex items-center justify-center h-32">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-muted-foreground">Loading students...</p>
                      </div>
                    </div>
                  ) : students.length === 0 ? (
                    <div className="text-center py-8">
                      <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                      <h3 className="text-lg font-medium mb-2">No students found</h3>
                      <p className="text-muted-foreground">No students match your current filters</p>
                    </div>
                  ) : (
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead>Student</TableHead>
                          <TableHead>Daily Logs</TableHead>
                          <TableHead>Weekly Reports</TableHead>
                          <TableHead>Current Week</TableHead>
                          <TableHead>Total Hours</TableHead>
                          <TableHead>Completion Rate</TableHead>
                          <TableHead>Last Activity</TableHead>
                          <TableHead>Actions</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {students.map((student) => (
                          <TableRow key={student.student_id}>
                            <TableCell>
                              <div>
                                <div className="font-medium">{student.student_name}</div>
                                <div className="text-sm text-muted-foreground">{student.email}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">
                                  {student.submitted_daily_logs}/{student.total_daily_logs}
                                </Badge>
                                <div className="text-xs text-muted-foreground">
                                  {student.total_daily_logs > 0
                                    ? Math.round((student.submitted_daily_logs / student.total_daily_logs) * 100)
                                    : 0}%
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <Badge variant="outline">
                                  {student.submitted_weekly_reports}/{student.total_weekly_reports}
                                </Badge>
                                <div className="text-xs text-muted-foreground">
                                  {student.total_weekly_reports > 0
                                    ? Math.round((student.submitted_weekly_reports / student.total_weekly_reports) * 100)
                                    : 0}%
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">Week {student.current_week}</Badge>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {student.total_hours}h
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <div className="text-sm font-medium">{student.completion_rate}%</div>
                                <div className="w-16 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-primary h-2 rounded-full"
                                    style={{ width: `${student.completion_rate}%` }}
                                  ></div>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              {student.last_activity ? formatDate(student.last_activity) : '-'}
                            </TableCell>
                            <TableCell>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => loadStudentDetails(student)}
                                className="flex items-center gap-2"
                              >
                                <Eye className="h-4 w-4" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </CardContent>
              </Card>
            ) : (
              /* Student Details View */
              <Tabs value={studentDetailTab} onValueChange={setStudentDetailTab}>
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="daily-logs" className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4" />
                    Daily Logs ({studentDailyLogs.length})
                  </TabsTrigger>
                  <TabsTrigger value="weekly-reports" className="flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Weekly Reports ({studentWeeklyReports.length})
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="daily-logs" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Daily Log Entries for {selectedStudent?.student_name}</CardTitle>
                      <CardDescription>
                        {selectedStudent?.submitted_daily_logs}/{selectedStudent?.total_daily_logs} daily logs submitted
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {loading ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                            <p className="text-muted-foreground">Loading daily logs...</p>
                          </div>
                        </div>
                      ) : studentDailyLogs.length === 0 ? (
                        <div className="text-center py-8">
                          <BookOpen className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                          <h3 className="text-lg font-medium mb-2">No daily logs found</h3>
                          <p className="text-muted-foreground">This student hasn't created any daily log entries yet</p>
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Week</TableHead>
                              <TableHead>Day</TableHead>
                              <TableHead>Title</TableHead>
                              <TableHead>Description</TableHead>
                              <TableHead>Hours</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Date</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {studentDailyLogs.map((log) => (
                              <TableRow key={log.id}>
                                <TableCell>
                                  <Badge variant="outline">Week {log.week_number}</Badge>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="secondary">{log.day_of_week}</Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="max-w-[200px] truncate font-medium">{log.title}</div>
                                </TableCell>
                                <TableCell>
                                  <div className="max-w-[300px] truncate text-sm text-muted-foreground">
                                    {log.description || 'No description'}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {log.hours_worked}h
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={log.is_submitted ? "default" : "secondary"}>
                                    {log.is_submitted ? "Submitted" : "Draft"}
                                  </Badge>
                                </TableCell>
                                <TableCell>{formatDate(log.date)}</TableCell>
                                <TableCell>
                                  <Button variant="ghost" size="sm">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>

                <TabsContent value="weekly-reports" className="space-y-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Weekly Reports for {selectedStudent?.student_name}</CardTitle>
                      <CardDescription>
                        {selectedStudent?.submitted_weekly_reports}/{selectedStudent?.total_weekly_reports} weekly reports submitted
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {loading ? (
                        <div className="flex items-center justify-center h-32">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                            <p className="text-muted-foreground">Loading weekly reports...</p>
                          </div>
                        </div>
                      ) : studentWeeklyReports.length === 0 ? (
                        <div className="text-center py-8">
                          <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                          <h3 className="text-lg font-medium mb-2">No weekly reports found</h3>
                          <p className="text-muted-foreground">This student hasn't created any weekly reports yet</p>
                        </div>
                      ) : (
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead>Week</TableHead>
                              <TableHead>Summary</TableHead>
                              <TableHead>Submission Type</TableHead>
                              <TableHead>Status</TableHead>
                              <TableHead>Submitted</TableHead>
                              <TableHead>Actions</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {studentWeeklyReports.map((report) => (
                              <TableRow key={report.id}>
                                <TableCell>
                                  <Badge variant="outline">Week {report.week_number}</Badge>
                                </TableCell>
                                <TableCell>
                                  <div className="max-w-[400px] truncate">
                                    {report.summary || 'No summary provided'}
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Badge variant="secondary">
                                    {report.submission_type === 'upload' ? 'File Upload' : 'Written'}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  <Badge variant={report.is_submitted ? "default" : "secondary"}>
                                    {report.is_submitted ? "Submitted" : "Draft"}
                                  </Badge>
                                </TableCell>
                                <TableCell>
                                  {report.submitted_at ? formatDate(report.submitted_at) : '-'}
                                </TableCell>
                                <TableCell>
                                  <Button variant="ghost" size="sm">
                                    <Eye className="h-4 w-4" />
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      )}
                    </CardContent>
                  </Card>
                </TabsContent>
              </Tabs>
            )}
          </div>
        </div>
      </div>
      </div>
  )
}
