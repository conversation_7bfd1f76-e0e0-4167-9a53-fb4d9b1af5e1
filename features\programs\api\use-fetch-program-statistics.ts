import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program } from "../server/Program";

export const useFetchProgramStatistics = () => {
    const query = useQuery({
        queryKey: ["program-statistics"],
        queryFn: async () => {
            try {
                const res = await Program.getStatistics();
                if (res.success && res.data) {
                    return res.data;
                }
                return {
                    overview: {
                        total_programs: 0,
                        total_students_enrolled: 0,
                        most_popular_program: null
                    },
                    programs: []
                };
            } catch (error: any) {
                console.error('Failed to fetch program statistics:', error);
                return {
                    overview: {
                        total_programs: 0,
                        total_students_enrolled: 0,
                        most_popular_program: null
                    },
                    programs: []
                };
            }
        },
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });
    
    return query;
};
