// Replace all occurrences of localStorage.getItem('token') with localStorage.getItem('iptms_token')
import axios from "axios"
import { toast } from "sonner"
import { DailyLog, WeeklyReport } from "../types/logbook"

export class IPTLogbooks {
  private static api_url = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api"

  // Daily Log methods
  public static async getMyDailyLogs() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/daily-logs`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch daily logs")
      throw error.response?.data
    }
  }

  public static async getWeekDailyLogs(weekNumber: number) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/daily-logs/week/${weekNumber}`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch week daily logs")
      throw error.response?.data
    }
  }

  public static async createDailyLog(dailyLog: DailyLog) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.post(`${this.api_url}/ipt-logbook/daily-log`, dailyLog, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Daily log created successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to create daily log")
      throw error.response?.data
    }
  }

  public static async updateDailyLog(id: string, dailyLog: Partial<DailyLog>) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.put(`${this.api_url}/ipt-logbook/daily-log/${id}`, dailyLog, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Daily log updated successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update daily log")
      throw error.response?.data
    }
  }

  public static async deleteDailyLog(id: string) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.delete(`${this.api_url}/ipt-logbook/daily-log/${id}`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Daily log deleted successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to delete daily log")
      throw error.response?.data
    }
  }

  // Weekly Report methods
  public static async getMyWeeklyReports() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/weekly-reports`, {
        headers: {
          'Authorization':token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly reports")
      throw error.response?.data
    }
  }

  public static async getWeeklyReport(weekNumber: number) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/weekly-report/week/${weekNumber}`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly report")
      throw error.response?.data
    }
  }

  public static async createWeeklyReport(weeklyReport: WeeklyReport) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.post(`${this.api_url}/ipt-logbook/weekly-report`, weeklyReport, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200 || response.status === 201) {
        toast.success(response.data.message || "Weekly report created successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to create weekly report")
      throw error.response?.data
    }
  }

  public static async updateWeeklyReport(id: string, weeklyReport: Partial<WeeklyReport>) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.put(`${this.api_url}/ipt-logbook/weekly-report/${id}`, weeklyReport, {
        headers: {
          'Authorization': token,
          'Content-Type': 'application/json'
        }
      })

      if (response.status === 200) {
        toast.success(response.data.message || "Weekly report updated successfully")
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to update weekly report")
      throw error.response?.data
    }
  }

  // Admin methods
  public static async getAllDailyLogs(filters?: any) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/admin/reports/ipt-daily-logs`, {
        params: filters,
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch daily logs")
      throw error.response?.data
    }
  }

  public static async getAllWeeklyReports(filters?: any) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/admin/weekly-reports`, {
        params: filters,
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch weekly reports")
      throw error.response?.data
    }
  }

  public static async getLogbookStatistics() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/admin/statistics`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch logbook statistics")
      throw error.response?.data
    }
  }

  public static async getStudentLogbookSummaries(filters?: any) {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/admin/student-summaries`, {
        params: filters,
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch student summaries")
      throw error.response?.data
    }
  }



  // Get student logbook progress
  public static async getStudentProgress() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/ipt-logbook/student-progress`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch student progress")
      throw error.response?.data
    }
  }

  // Utility methods
  public static getDayName(dayOfWeek: string): string {
    const days = {
      'monday': 'Monday',
      'tuesday': 'Tuesday',
      'wednesday': 'Wednesday',
      'thursday': 'Thursday',
      'friday': 'Friday',
      'saturday': 'Saturday'
    }
    return days[dayOfWeek as keyof typeof days] || dayOfWeek
  }

  public static getWeekDates(weekNumber: number, year: number = new Date().getFullYear()) {
    // Calculate the start date of the IPT (assuming it starts on a Monday)
    // This is a simplified calculation - in reality, you'd want to store the IPT start date
    const startDate = new Date(year, 0, 1) // January 1st
    const daysToAdd = (weekNumber - 1) * 7
    const weekStart = new Date(startDate.getTime() + daysToAdd * 24 * 60 * 60 * 1000)
    
    // Adjust to Monday
    const dayOfWeek = weekStart.getDay()
    const daysToMonday = dayOfWeek === 0 ? -6 : 1 - dayOfWeek
    weekStart.setDate(weekStart.getDate() + daysToMonday)
    
    const weekEnd = new Date(weekStart)
    weekEnd.setDate(weekStart.getDate() + 6) // Sunday
    
    return {
      start: weekStart.toISOString().split('T')[0],
      end: weekEnd.toISOString().split('T')[0]
    }
  }
}
