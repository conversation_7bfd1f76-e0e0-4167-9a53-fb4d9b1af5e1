import { useQuery } from "@tanstack/react-query";
import { Student } from "../server/Student";
import { useGlobalManager } from "@/hooks/use-context";

export const useRetrieveStudent = (id: string) => {
    const { iptId } = useGlobalManager();

    const query = useQuery({
        enabled: !!id && !!iptId,
        queryKey: ["students", iptId, id],
        queryFn: async () => {
          const res = await Student.show(id);

          // if (!res.ok) {
          //   return null;
          // }
          // console.log(res)
          return res[0];
        },
    });

    return query;
}