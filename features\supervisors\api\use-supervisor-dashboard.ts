import { useQuery } from "@tanstack/react-query";
import { Supervisor } from "../server/Supervisor";
import { useGlobalManager } from "@/hooks/use-context";

export const useSupervisorDashboardStats = () => {
  const { iptId } = useGlobalManager();

  return useQuery({
    queryKey: ["supervisor", "dashboard", "stats", iptId],
    queryFn: async () => {
      const response = await Supervisor.getDashboardStats();
      return response.data;
    },
    enabled: !!iptId, // Only run query when IPT is selected
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useAssignedStudents = () => {
  const { iptId } = useGlobalManager();

  return useQuery({
    queryKey: ["supervisor", "students", iptId],
    queryFn: async () => {
      const response = await Supervisor.getAssignedStudents();
      return response.data;
    },
    enabled: !!iptId, // Only run query when IPT is selected
  });
};

export const useStudentDetails = (studentId: string | null) => {
  return useQuery({
    queryKey: ["supervisor", "student", studentId],
    queryFn: async () => {
      if (!studentId) throw new Error("Student ID is required");
      const response = await Supervisor.getStudentDetails(studentId);
      return response.data;
    },
    enabled: !!studentId,
  });
};
