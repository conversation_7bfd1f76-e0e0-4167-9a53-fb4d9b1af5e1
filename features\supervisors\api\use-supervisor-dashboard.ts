import { useQuery } from "@tanstack/react-query";
import { Supervisor } from "../server/Supervisor";

export const useSupervisorDashboardStats = () => {
  return useQuery({
    queryKey: ["supervisor", "dashboard", "stats"],
    queryFn: async () => {
      const response = await Supervisor.getDashboardStats();
      return response.data;
    },
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

export const useAssignedStudents = () => {
  return useQuery({
    queryKey: ["supervisor", "students"],
    queryFn: async () => {
      const response = await Supervisor.getAssignedStudents();
      return response.data;
    },
  });
};

export const useStudentDetails = (studentId: string | null) => {
  return useQuery({
    queryKey: ["supervisor", "student", studentId],
    queryFn: async () => {
      if (!studentId) throw new Error("Student ID is required");
      const response = await Supervisor.getStudentDetails(studentId);
      return response.data;
    },
    enabled: !!studentId,
  });
};
