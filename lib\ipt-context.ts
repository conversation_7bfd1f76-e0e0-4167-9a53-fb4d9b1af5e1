/**
 * IPT Context Utility
 * Provides utilities for managing IPT context in API requests
 */

// Global IPT ID storage for server-side classes
let currentIptId: string | null = null;

/**
 * Set the current IPT ID
 */
export const setCurrentIptId = (iptId: string | null) => {
  currentIptId = iptId;
};

/**
 * Get the current IPT ID
 */
export const getCurrentIptId = (): string | null => {
  return currentIptId;
};

/**
 * Add IPT context to request parameters
 */
export const addIptContext = (params: Record<string, any> = {}): Record<string, any> => {
  if (currentIptId) {
    return { ...params, ipt_id: currentIptId };
  }
  return params;
};

/**
 * Add IPT context to request body
 */
export const addIptContextToBody = (body: Record<string, any> = {}): Record<string, any> => {
  if (currentIptId) {
    return { ...body, ipt_id: currentIptId };
  }
  return body;
};

/**
 * Add IPT context to URL path
 */
export const addIptContextToPath = (basePath: string): string => {
  if (currentIptId) {
    const separator = basePath.includes('?') ? '&' : '?';
    return `${basePath}${separator}ipt_id=${currentIptId}`;
  }
  return basePath;
};

/**
 * Get authorization headers with IPT context
 */
export const getAuthHeadersWithIptContext = (): Record<string, string> => {
  const token = localStorage.getItem('iptms_token');
  const headers: Record<string, string> = {
    'Content-Type': 'application/json'
  };
  
  if (token) {
    headers['Authorization'] = token;
  }
  
  if (currentIptId) {
    headers['X-IPT-ID'] = currentIptId;
  }
  
  return headers;
};

/**
 * Check if IPT context is available
 */
export const hasIptContext = (): boolean => {
  return currentIptId !== null;
};
