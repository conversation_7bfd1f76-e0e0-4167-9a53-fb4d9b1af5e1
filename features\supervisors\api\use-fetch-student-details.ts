import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useFetchStudentDetails = (student_id: string) => {
    const query = useQuery({
        queryKey: ["supervisor-student-details", student_id],
        queryFn: async () => {
            try {
                const res = await Supervisor.getStudentDetails(student_id);
                if (res.success && res.data) {
                    return res.data;
                }
                return null;
            } catch (error: any) {
                console.error('Failed to fetch student details:', error);
                return null;
            }
        },
        enabled: !!student_id, // Only run query if student_id is provided
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });
    
    return query;
};
