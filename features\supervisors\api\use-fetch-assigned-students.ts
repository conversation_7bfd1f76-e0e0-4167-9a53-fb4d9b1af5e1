import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useFetchAssignedStudents = () => {
    console.log('=== useFetchAssignedStudents hook called ===');

    const query = useQuery({
        queryKey: ["supervisor-assigned-students"],
        queryFn: async () => {
            console.log('=== useFetchAssignedStudents queryFn executing ===');
            try {
                console.log('Calling Supervisor.getAssignedStudents()...');
                const res = await Supervisor.getAssignedStudents();
                console.log('Response from Supervisor.getAssignedStudents():', res);
                if (res.success && res.data) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                console.error('Failed to fetch assigned students:', error);
                return [];
            }
        },
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });

    console.log('useFetchAssignedStudents query state:', {
        isLoading: query.isLoading,
        isError: query.isError,
        data: query.data,
        error: query.error
    });

    return query;
};
