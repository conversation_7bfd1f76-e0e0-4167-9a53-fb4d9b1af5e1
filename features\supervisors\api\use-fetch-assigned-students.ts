import { useQuery } from "@tanstack/react-query";

export const useFetchAssignedStudents = () => {
    console.log('=== useFetchAssignedStudents hook called ===');

    const query = useQuery({
        queryKey: ["supervisor-assigned-students"],
        queryFn: async () => {
            console.log('=== useFetchAssignedStudents queryFn executing ===');
            try {
                console.log('Calling direct API endpoint...');

                // Use the test endpoint that we know works
                console.log('API URL:', process.env.NEXT_PUBLIC_API_URL);
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/other/test-students`);

                const data = await response.json();
                console.log('Direct API response:', data);

                if (data.success && data.data) {
                    return data.data;
                }
                return [];
            } catch (error: any) {
                console.error('Failed to fetch assigned students:', error);
                return [];
            }
        },
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });

    console.log('useFetchAssignedStudents query state:', {
        isLoading: query.isLoading,
        isError: query.isError,
        data: query.data,
        error: query.error
    });

    return query;
};
