import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useFetchAssignedStudents = () => {
    const query = useQuery({
        queryKey: ["supervisor-assigned-students"],
        queryFn: async () => {
            try {
                const res = await Supervisor.getAssignedStudents();
                if (res.success && res.data) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                console.error('Failed to fetch assigned students:', error);
                return [];
            }
        },
        refetchInterval: 30000, // Refetch every 30 seconds
        staleTime: 10000, // Consider data stale after 10 seconds
    });
    
    return query;
};
