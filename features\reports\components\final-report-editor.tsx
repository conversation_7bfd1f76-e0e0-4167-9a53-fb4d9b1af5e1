"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { FinalReport, FileUpload } from "../types/reports"
import { RichTextEditor } from "@/features/logbook/components/rich-text-editor"
import { FileUploadComponent } from "@/features/logbook/components/file-upload"
import { ConfirmationDialog } from "@/features/logbook/components/confirmation-dialog"
import { toast } from "sonner"
import { Save, Send, FileText, Edit3, GraduationCap } from "lucide-react"
import { FinalReports } from "../server/FinalReports"

interface FinalReportEditorProps {
  initialReport?: FinalReport
  onSave: (report: FinalReport) => void
  onSubmit?: (report: FinalReport) => void
  disabled?: boolean
}

export function FinalReportEditor({
  initialReport,
  onSave,
  onSubmit,
  disabled = false
}: FinalReportEditorProps) {
  const [submissionType, setSubmissionType] = useState<'write' | 'upload'>(
    initialReport?.submission_type || 'write'
  )
  const [content, setContent] = useState(initialReport?.content || "")
  const [uploadedFile, setUploadedFile] = useState<FileUpload | undefined>()
  const [showSubmitDialog, setShowSubmitDialog] = useState(false)
  const [saving, setSaving] = useState(false)

  // Initialize state from initial report
  useEffect(() => {
    if (initialReport) {
      setSubmissionType(initialReport.submission_type)
      setContent(initialReport.content || "")
      
      // If there's a file, create a mock FileUpload object
      if (initialReport.file_url && initialReport.file_name) {
        setUploadedFile({
          file: new File([], initialReport.file_name),
          preview: initialReport.file_url
        })
      }
    }
  }, [initialReport])

  const validateReport = (showToast: boolean = true) => {
    if (submissionType === 'write') {
      const textContent = content.replace(/<[^>]*>/g, '').trim()
      if (textContent.length < 500) {
        if (showToast) {
          toast.error("Final report must be at least 500 characters long")
        }
        return false
      }
    } else if (submissionType === 'upload') {
      if (!uploadedFile) {
        if (showToast) {
          toast.error("Please upload a file for your final report")
        }
        return false
      }
    }
    return true
  }

  const handleSave = async () => {
    if (!validateReport()) return

    setSaving(true)
    try {
      const report: FinalReport = {
        ...initialReport,
        submission_type: submissionType,
        content: submissionType === 'write' ? content : undefined,
        file_name: submissionType === 'upload' ? uploadedFile?.file.name : undefined,
        file_size: submissionType === 'upload' ? uploadedFile?.file.size : undefined,
        file_type: submissionType === 'upload' ? uploadedFile?.file.type : undefined,
        character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
        is_submitted: false
      }

      console.log('finale', report);
      
      await onSave(report)
      toast.success("Final report saved successfully")
    } catch (error) {
      toast.error("Failed to save final report")
    } finally {
      setSaving(false)
    }
  }
  
  const handleSubmit = async () => {
    if (!validateReport()) return
    
    setSaving(true)
    try {
      const report: FinalReport = {
        ...initialReport,
        submission_type: submissionType,
        content: submissionType === 'write' ? content : undefined,
        file_name: submissionType === 'upload' ? uploadedFile?.file.name : undefined,
        file_size: submissionType === 'upload' ? uploadedFile?.file.size : undefined,
        file_type: submissionType === 'upload' ? uploadedFile?.file.type : undefined,
        character_count: submissionType === 'write' ? content.replace(/<[^>]*>/g, '').length : undefined,
        is_submitted: true,
        student_id: initialReport?.student_id ?? "" // Ensure student_id is always a string
      }

      
      if (onSubmit) {
        await onSubmit(report)
        await FinalReports.createFinalReport(report as any);
      } else {
        await onSave(report)
      }
      
      toast.success("Final report submitted successfully")
    } catch (error) {
      toast.error("Failed to submit final report")
    } finally {
      setSaving(false)
    }
  }

  const handleFileSelect = (file: FileUpload) => {
    setUploadedFile(file)
  }

  const handleFileRemove = () => {
    setUploadedFile(undefined)
  }

  const isSubmitted = initialReport?.is_submitted || false
  const canEdit = !isSubmitted && !disabled

  return (
    <>
      <Card className="w-full">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <GraduationCap className="h-6 w-6 text-primary" />
              <CardTitle>Final Internship Report</CardTitle>
              {isSubmitted && (
                <Badge variant="secondary" className="bg-green-100 text-green-800">
                  Submitted
                </Badge>
              )}
            </div>
            
            {submissionType === 'write' && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <Edit3 className="h-4 w-4" />
                <span>{content.replace(/<[^>]*>/g, '').length} characters</span>
              </div>
            )}
            
            {submissionType === 'upload' && uploadedFile && (
              <div className="flex items-center space-x-2 text-sm text-muted-foreground">
                <FileText className="h-4 w-4" />
                <span>{uploadedFile.file.name}</span>
              </div>
            )}
          </div>
        </CardHeader>
        
        <CardContent>
          <Tabs 
            value={submissionType} 
            onValueChange={(value) => canEdit && setSubmissionType(value as 'write' | 'upload')}
            className="w-full"
          >
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="write" disabled={!canEdit}>
                <Edit3 className="h-4 w-4 mr-2" />
                Write Report
              </TabsTrigger>
              <TabsTrigger value="upload" disabled={!canEdit}>
                <FileText className="h-4 w-4 mr-2" />
                Upload File
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="write" className="mt-6">
              <RichTextEditor
                value={content}
                onChange={setContent}
                placeholder="Write your comprehensive final report here. Include your learning outcomes, project achievements, challenges faced, skills developed, and overall internship experience..."
                minHeight="500px"
                maxLength={10000}
                showCharacterCount={true}
              />
              
              <div className="mt-4 text-sm text-muted-foreground">
                <p>Minimum 500 characters required. This is your opportunity to showcase your internship experience and learning outcomes.</p>
              </div>
            </TabsContent>
            
            <TabsContent value="upload" className="mt-6">
              <FileUploadComponent
                onFileSelect={handleFileSelect}
                onFileRemove={handleFileRemove}
                currentFile={uploadedFile}
                maxSize={25}
                acceptedTypes={['.pdf', '.doc', '.docx']}
              />
              
              <div className="mt-4 text-sm text-muted-foreground">
                <p>Upload your final report as a PDF, DOC, or DOCX file (max 25MB). Ensure your document includes all required sections.</p>
              </div>
            </TabsContent>
          </Tabs>
          
          {canEdit && (
            <div className="flex items-center justify-between mt-6 pt-4 border-t">
              <div className="text-sm text-muted-foreground">
                <span>
                  {submissionType === 'write' 
                    ? "Use the formatting tools to structure your comprehensive final report." 
                    : "Supported formats: PDF, DOC, DOCX (max 25MB)"
                  }
                </span>
              </div>
              
              <div className="flex items-center space-x-3">
                <Button
                  variant="outline"
                  onClick={handleSave}
                  disabled={saving}
                  className="flex items-center space-x-2"
                >
                  <Save className="h-4 w-4" />
                  <span>{saving ? "Saving..." : "Save Draft"}</span>
                </Button>
                
                <Button
                  onClick={() => setShowSubmitDialog(true)}
                  disabled={saving || !validateReport(false)}
                  className="flex items-center space-x-2"
                >
                  <Send className="h-4 w-4" />
                  <span>Submit Final Report</span>
                </Button>
              </div>
            </div>
          )}
          
          {isSubmitted && (
            <div className="mt-6 pt-4 border-t">
              <div className="flex items-center justify-center text-sm text-muted-foreground">
                <span>This final report has been submitted and cannot be edited.</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <ConfirmationDialog
        isOpen={showSubmitDialog}
        onClose={() => setShowSubmitDialog(false)}
        onConfirm={handleSubmit}
        title="Submit Final Report"
        description="Are you sure you want to submit your final report? This is your final submission for the internship program and cannot be changed after submission."
        confirmText="Submit Final Report"
        cancelText="Cancel"
      />
    </>
  )
}
