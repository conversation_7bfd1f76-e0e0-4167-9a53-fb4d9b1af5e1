import axios from "axios";
import { toast } from "sonner";

export interface SupervisorDashboardStats {
  total_students: number;
  students_with_logs_this_week: number;
  students_logged_this_week: number;
  students_with_submitted_reports: number;
  students_with_pending_reports: number;
  students_with_final_reports: number;
  students_with_supervisor_feedback: number;
}

export interface AssignedStudent {
  student_id: string;
  user_id: string;
  first_name: string;
  middle_name?: string;
  last_name: string;
  email: string;
  phone?: string;
  reg_no: string;
  class: string;
  program_name?: string;
  program_id?: string;
  student_name: string;
  created_at: string;
  updated_at: string;
}

export interface StudentDetails {
  student: AssignedStudent;
  daily_log_entries: DailyLogEntry[];
  weekly_reports: WeeklyReport[];
  final_report: FinalReport | null;
  feedback: Feedback[];
}

export interface DailyLogEntry {
  id: string;
  student_id: string;
  date: string;
  formatted_date: string;
  title: string;
  activities: string;
  hours_worked: number;
  week_number: number;
  is_submitted: boolean;
  supervisor_feedback?: string;
  created_at: string;
  updated_at: string;
}

export interface WeeklyReport {
  id: string;
  student_id: string;
  week_number: number;
  summary: string;
  objectives_achieved?: string;
  challenges_encountered?: string;
  skills_developed?: string;
  learning_outcomes?: string;
  areas_for_improvement?: string;
  next_week_goals?: string;
  total_hours: number;
  attendance_days: number;
  self_assessment?: string;
  self_rating?: number;
  supervisor_comments?: string;
  supervisor_rating?: number;
  is_submitted: boolean;
  created_at: string;
  updated_at: string;
}

export interface FinalReport {
  id: string;
  student_id: string;
  title?: string;
  content?: string;
  file_name?: string;
  file_url?: string;
  is_submitted: boolean;
  supervisor_comments?: string;
  supervisor_rating?: number;
  grade?: string;
  supervisor_feedback_at?: string;
  created_at: string;
  updated_at: string;
}

export interface Feedback {
  id: string;
  student_id: string;
  category: string;
  content: string;
  rating?: number;
  is_submitted: boolean;
  created_at: string;
  updated_at: string;
}

export class SupervisorAPI {
  private static api_url = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api";

  private static getAuthHeaders() {
    const token = localStorage.getItem('iptms_token');
    return {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
  }

  public static async getDashboardStats(): Promise<{ data: SupervisorDashboardStats }> {
    try {
      const response = await axios.get(
        `${this.api_url}/other/supervisors/dashboard/stats`,
        { headers: this.getAuthHeaders() }
      );

      if (response.status === 200) {
        return response.data;
      }
      throw new Error('Failed to fetch dashboard stats');
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch dashboard statistics");
      throw error.response?.data || error;
    }
  }

  public static async getAssignedStudents(): Promise<{ data: AssignedStudent[] }> {
    try {
      const response = await axios.get(
        `${this.api_url}/other/supervisors/students`,
        { headers: this.getAuthHeaders() }
      );

      if (response.status === 200) {
        return response.data;
      }
      throw new Error('Failed to fetch assigned students');
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch assigned students");
      throw error.response?.data || error;
    }
  }

  public static async getStudentDetails(studentId: string): Promise<{ data: StudentDetails }> {
    try {
      const response = await axios.get(
        `${this.api_url}/other/supervisors/students/${studentId}`,
        { headers: this.getAuthHeaders() }
      );

      if (response.status === 200) {
        return response.data;
      }
      throw new Error('Failed to fetch student details');
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to fetch student details");
      throw error.response?.data || error;
    }
  }

  public static async addDailyLogFeedback(
    dailyLogId: string, 
    feedback: string
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post(
        `${this.api_url}/other/supervisors/daily-logs/${dailyLogId}/feedback`,
        { supervisor_feedback: feedback },
        { headers: this.getAuthHeaders() }
      );

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data;
      }
      throw new Error('Failed to add feedback');
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to add feedback");
      throw error.response?.data || error;
    }
  }

  public static async addFinalReportFeedback(
    finalReportId: string,
    data: {
      supervisor_comments: string;
      supervisor_rating?: number;
      grade?: string;
    }
  ): Promise<{ success: boolean; message: string }> {
    try {
      const response = await axios.post(
        `${this.api_url}/other/supervisors/final-reports/${finalReportId}/feedback`,
        data,
        { headers: this.getAuthHeaders() }
      );

      if (response.status === 200) {
        toast.success(response.data.message);
        return response.data;
      }
      throw new Error('Failed to add final report feedback');
    } catch (error: any) {
      toast.error(error.response?.data?.message || "Failed to add final report feedback");
      throw error.response?.data || error;
    }
  }
}
