import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program, ProgramData } from "../server/Program";

export const useCreateProgram = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<any, Error, ProgramData>({ 
    mutationFn: async (data: ProgramData) => {
      const res = await Program.create(data);
      return res;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
    },
    onError: (error) => {
      console.error('Create program error:', error);
    },
  });

  return mutation;
};
