import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program } from "../server/Program";

export const useDeleteProgram = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<any, Error, { id: string }>({ 
    mutationFn: async ({ id }: { id: string }) => {
      const res = await Program.destroy(id);
      return res;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
    },
    onError: (error) => {
      console.error('Delete program error:', error);
    },
  });

  return mutation;
};
