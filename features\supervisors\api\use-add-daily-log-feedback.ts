import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useAddDailyLogFeedback = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation<any, Error, { daily_log_id: string; supervisor_feedback: string }>({ 
        mutationFn: async ({ daily_log_id, supervisor_feedback }: { daily_log_id: string; supervisor_feedback: string }) => {
            const res = await Supervisor.addDailyLogFeedback(daily_log_id, supervisor_feedback);
            return res;
        },
        onSuccess: (data, variables) => {
            // Invalidate related queries
            queryClient.invalidateQueries({ queryKey: ["supervisor-student-details"] });
            queryClient.invalidateQueries({ queryKey: ["supervisor-assigned-students"] });
        },
        onError: (error) => {
            console.error('Add daily log feedback error:', error);
        },
    });

    return mutation;
};
