import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program } from "../server/Program";
import { useGlobalManager } from "@/hooks/use-context";

export const useFetchPrograms = () => {
    const { iptId } = useGlobalManager();

    const query = useQuery({
        queryKey: ["programs", iptId],
        queryFn: async () => {
            try {
                const res = await Program.index();
                if (res.success && res.data) {
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
        enabled: !!iptId, // Only run query when IPT is selected
        // initialData: []
    });
    return query;
}
