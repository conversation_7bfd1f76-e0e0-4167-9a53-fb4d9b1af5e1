import axios from "axios"
import { toast } from "sonner"

export interface AdminStatistics {
  total_students: number
  total_supervisors: number
  total_arrival_notes: number
  submitted_arrival_notes: number
  arrival_note_submission_rate: number
  total_daily_logs: number
  submitted_daily_logs: number
  total_weekly_reports: number
  submitted_weekly_reports: number
  total_final_reports: number
  submitted_final_reports: number
  active_students: number
  current_week: number
  average_hours_per_day: number
}

export interface RecentActivity {
  id: string
  type: 'arrival_note' | 'daily_log' | 'weekly_report' | 'final_report'
  student_name: string
  student_email: string
  title: string
  submitted_at: string
}

export class AdminDashboard {
  private static api_url = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8080/api"

  // Get admin dashboard statistics
  public static async getStatistics() {
    try {
      const token = localStorage.getItem('iptms_token')
      
      // Get logbook statistics
      const logbookResponse = await axios.get(`${this.api_url}/ipt-logbook/admin/statistics`, {
        headers: {
          'Authorization': token
        }
      })

      // Get final report statistics
      const finalReportResponse = await axios.get(`${this.api_url}/final-reports/admin/final-report-statistics`, {
        headers: {
          'Authorization': token
        }
      })

      // Get user statistics
      const userResponse = await axios.get(`${this.api_url}/admin/users/statistics`, {
        headers: {
          'Authorization': token
        }
      })

      const logbookStats = logbookResponse.data?.data || {}
      const finalReportStats = finalReportResponse.data?.data || {}
      const userStats = userResponse.data?.data || {}

      const statistics: AdminStatistics = {
        total_students: userStats.total_students || 0,
        total_supervisors: userStats.total_supervisors || 0,
        total_arrival_notes: userStats.total_arrival_notes || 0,
        submitted_arrival_notes: userStats.submitted_arrival_notes || 0,
        arrival_note_submission_rate: userStats.arrival_note_submission_rate || 0,
        total_daily_logs: logbookStats.total_daily_logs || 0,
        submitted_daily_logs: logbookStats.submitted_daily_logs || 0,
        total_weekly_reports: logbookStats.total_weekly_reports || 0,
        submitted_weekly_reports: logbookStats.submitted_weekly_reports || 0,
        total_final_reports: finalReportStats.total_reports || 0,
        submitted_final_reports: finalReportStats.submitted_reports || 0,
        active_students: logbookStats.active_students || 0,
        current_week: logbookStats.current_week || 1,
        average_hours_per_day: logbookStats.average_hours_per_day || 0
      }

      return {
        success: true,
        data: statistics
      }
    } catch (error: any) {
      console.error('Failed to fetch admin statistics:', error)
      // Don't show toast error for statistics as it's not critical
      return {
        success: false,
        data: null
      }
    }
  }

  // Get recent activities
  public static async getRecentActivities() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/admin/recent-activities`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      console.error('Failed to fetch recent activities:', error)
      return {
        success: false,
        data: []
      }
    }
  }

  // Get placement distribution
  public static async getPlacementDistribution() {
    try {
      const token = localStorage.getItem('iptms_token')
      const response = await axios.get(`${this.api_url}/admin/placement-distribution`, {
        headers: {
          'Authorization': token
        }
      })

      if (response.status === 200) {
        return response.data
      }
    } catch (error: any) {
      console.error('Failed to fetch placement distribution:', error)
      return {
        success: false,
        data: []
      }
    }
  }
}
