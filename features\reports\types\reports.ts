export interface FinalReport {
  id?: string;
  student_id?: string;
  title?: string;
  submission_type: 'write' | 'upload';

  // Single content field for rich text editor
  content_html?: string;
  content_plain?: string;

  // File upload fields
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;

  // Metadata
  character_count?: number;
  word_count?: number;

  // Status and workflow
  status?: 'draft' | 'submitted' | 'reviewed' | 'approved' | 'needs_revision';
  is_submitted?: boolean;
  submitted_at?: string;
  reviewed_at?: string;

  // Supervisor feedback
  supervisor_id?: string;
  supervisor_comments?: string;
  supervisor_rating?: number;
  grade?: string;

  // Timestamps
  created_at?: string;
  updated_at?: string;
}

export interface Feedback {
  id?: string;
  student_id?: string;
  category: 'general' | 'program' | 'supervision' | 'facilities' | 'recommendations';
  title: string;
  content?: string;
  submission_type: 'write' | 'upload';
  file_url?: string;
  file_name?: string;
  file_size?: number;
  file_type?: string;
  character_count?: number;
  rating?: number; // 1-5 scale
  is_submitted?: boolean;
  submitted_at?: string;
  created_at?: string;
  updated_at?: string;
}

export interface FeedbackCategory {
  value: 'general' | 'program' | 'supervision' | 'facilities' | 'recommendations';
  label: string;
  description: string;
  icon?: string;
}

export interface FileUpload {
  file: File;
  preview?: string;
  progress?: number;
  error?: string;
}

// Admin view types
export interface StudentReportSummary {
  student_id: string;
  student_name: string;
  student_email: string;
  program_name?: string;
  supervisor_name?: string;
  
  // Logbook data
  total_daily_logs: number;
  total_weekly_reports: number;
  weeks_completed: number;
  last_logbook_update: string;
  
  // Final report
  final_report_status: 'not_started' | 'draft' | 'submitted';
  final_report_submitted_at?: string;
  
  // Feedback
  feedback_count: number;
  feedback_submitted_at?: string;
  
  // Overall status
  overall_completion: number; // percentage
  created_at: string;
  updated_at: string;
}

export interface AdminLogbookView {
  student_id: string;
  student_name: string;
  week_number: number;
  daily_logs: Array<{
    id: string;
    day_number: number;
    day_name: string;
    title: string;
    hours: string;
    status: string;
    created_at: string;
  }>;
  weekly_report: {
    id?: string;
    submission_type?: 'write' | 'upload';
    content?: string;
    file_name?: string;
    is_submitted: boolean;
    submitted_at?: string;
  };
}

export interface AdminReportFilters {
  student_name?: string;
  program?: string;
  supervisor?: string;
  status?: 'all' | 'completed' | 'pending' | 'overdue';
  submission_type?: 'all' | 'write' | 'upload';
  date_range?: {
    start: string;
    end: string;
  };
}
