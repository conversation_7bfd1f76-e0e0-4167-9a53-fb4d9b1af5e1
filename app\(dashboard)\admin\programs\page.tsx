"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Plus, Search, Users, BookOpen, MoreHorizontal, Edit, Trash2 } from "lucide-react"
import { useFetchPrograms } from "@/features/programs/api/use-fetch-programs"
import { useFetchProgramStatistics } from "@/features/programs/api/use-fetch-program-statistics"
import { useCreateProgram } from "@/features/programs/api/use-create-program"
import { useUpdateProgram } from "@/features/programs/api/use-update-program"
import { useDeleteProgram } from "@/features/programs/api/use-delete-program"
import { ProgramData } from "@/features/programs/server/Program"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"

const ProgramsPage = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false)
  const [isEditModalOpen, setIsEditModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectedProgram, setSelectedProgram] = useState<ProgramData | null>(null)
  const [formData, setFormData] = useState({ name: "", description: "" })

  // Hooks
  const { data: programs = [], isLoading } = useFetchPrograms()
  const { data: statistics, isLoading: isLoadingStats } = useFetchProgramStatistics()
  const createProgram = useCreateProgram()
  const updateProgram = useUpdateProgram()
  const deleteProgram = useDeleteProgram()

  // Filter programs based on search query
  const filteredPrograms = programs.filter((program: ProgramData) =>
    program.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (program.description && program.description.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Handle create program
  const handleCreate = async () => {
    if (!formData.name.trim()) return

    try {
      await createProgram.mutateAsync({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined
      })
      setIsCreateModalOpen(false)
      setFormData({ name: "", description: "" })
    } catch (error) {
      console.error('Failed to create program:', error)
    }
  }

  // Handle edit program
  const handleEdit = async () => {
    if (!selectedProgram || !formData.name.trim()) return

    try {
      await updateProgram.mutateAsync({
        id: selectedProgram.id!,
        data: {
          name: formData.name.trim(),
          description: formData.description.trim() || undefined
        }
      })
      setIsEditModalOpen(false)
      setSelectedProgram(null)
      setFormData({ name: "", description: "" })
    } catch (error) {
      console.error('Failed to update program:', error)
    }
  }

  // Handle delete program
  const handleDelete = async () => {
    if (!selectedProgram) return

    try {
      await deleteProgram.mutateAsync({ id: selectedProgram.id! })
      setIsDeleteModalOpen(false)
      setSelectedProgram(null)
    } catch (error) {
      console.error('Failed to delete program:', error)
    }
  }

  // Open edit modal
  const openEditModal = (program: ProgramData) => {
    setSelectedProgram(program)
    setFormData({
      name: program.name,
      description: program.description || ""
    })
    setIsEditModalOpen(true)
  }

  // Open delete modal
  const openDeleteModal = (program: ProgramData) => {
    setSelectedProgram(program)
    setIsDeleteModalOpen(true)
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">

            {/* Header */}
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Programs Management</h1>
              <p className="text-muted-foreground">
                Manage all academic programs in the system
              </p>
            </div>

            {/* Statistics Cards */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      statistics?.overview?.total_programs || programs.length
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Active programs
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Students Enrolled</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-12 rounded"></div>
                    ) : (
                      statistics?.overview?.total_students_enrolled || 0
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Across all programs
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Most Popular</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {isLoadingStats ? (
                      <div className="animate-pulse bg-muted h-8 w-20 rounded"></div>
                    ) : statistics?.overview?.most_popular_program && statistics.overview.most_popular_program.student_count > 0 ? (
                      <div className="flex flex-col">
                        <span className="text-xs font-bold max-w-[150px]" title={statistics.overview.most_popular_program.name}>
                          {statistics.overview.most_popular_program.name}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {statistics.overview.most_popular_program.student_count} student{statistics.overview.most_popular_program.student_count !== 1 ? 's' : ''}
                        </span>
                      </div>
                    ) : (
                      <span className="text-muted-foreground text-lg">No students yet</span>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Program with most students
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Actions Bar */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search programs..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-8 w-[300px]"
                  />
                </div>
              </div>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Add Program
              </Button>
            </div>

            {/* Programs Table */}
            <Card>
              <CardHeader>
                <CardTitle>Programs</CardTitle>
                <CardDescription>
                  A list of all programs in the system
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Name</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Students</TableHead>
                        <TableHead>Created</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredPrograms.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="text-muted-foreground">
                              {searchQuery ? "No programs found matching your search" : "No programs found"}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredPrograms.map((program: ProgramData) => (
                          <TableRow key={program.id}>
                            <TableCell className="font-medium">{program.name}</TableCell>
                            <TableCell>
                              {program.description ? (
                                <span className="text-sm text-muted-foreground">
                                  {program.description}
                                </span>
                              ) : (
                                <span className="text-sm text-muted-foreground italic">
                                  No description
                                </span>
                              )}
                            </TableCell>
                            <TableCell>
                              <Badge variant="secondary">
                                {program.student_count || 0}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              {program.created_at ? new Date(program.created_at).toLocaleDateString() : '-'}
                            </TableCell>
                            <TableCell className="text-right">
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" className="h-8 w-8 p-0">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => openEditModal(program)}>
                                    <Edit className="h-4 w-4 mr-2" />
                                    Edit
                                  </DropdownMenuItem>
                                  <DropdownMenuItem
                                    onClick={() => openDeleteModal(program)}
                                    className="text-red-600"
                                  >
                                    <Trash2 className="h-4 w-4 mr-2" />
                                    Delete
                                  </DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

          </div>
        </div>
      </div>

      {/* Create Program Modal */}
      <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Program</DialogTitle>
            <DialogDescription>
              Add a new academic program to the system
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="name">Program Name</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter program name"
                maxLength={45}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="description">Description (Optional)</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter program description"
                maxLength={45}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleCreate}
              disabled={!formData.name.trim() || createProgram.isPending}
            >
              {createProgram.isPending ? "Creating..." : "Create Program"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Program Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Program</DialogTitle>
            <DialogDescription>
              Update the program information
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="edit-name">Program Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                placeholder="Enter program name"
                maxLength={45}
              />
            </div>
            <div className="grid gap-2">
              <Label htmlFor="edit-description">Description (Optional)</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                placeholder="Enter program description"
                maxLength={45}
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditModalOpen(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleEdit}
              disabled={!formData.name.trim() || updateProgram.isPending}
            >
              {updateProgram.isPending ? "Updating..." : "Update Program"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Program Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Program</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete "{selectedProgram?.name}"? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteModalOpen(false)}>
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={deleteProgram.isPending}
            >
              {deleteProgram.isPending ? "Deleting..." : "Delete Program"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default ProgramsPage
