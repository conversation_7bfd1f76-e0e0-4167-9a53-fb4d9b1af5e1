"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Label } from "@/components/ui/label"
import { RichTextEditor } from "@/components/ui/rich-text-editor"
import { useGlobalManager } from "@/hooks/use-context"
import { FinalReports, FinalReport } from "@/features/reports/server/FinalReports"
import { toast } from "sonner"
import {
  FileText,
  Upload,
  Save,
  Send,
  Download,
  Edit,
  CheckCircle,
  AlertCircle
} from "lucide-react"

export default function FinalReportPage() {
  const { user } = useGlobalManager()
  const [finalReport, setFinalReport] = useState<FinalReport | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [submissionType, setSubmissionType] = useState<'write' | 'upload'>('write')
  const [reportData, setReportData] = useState<Partial<FinalReport>>({
    title: 'Final Internship Report',
    submission_type: 'write',
    content_html: '',
    content_plain: '',
    status: 'draft',
    is_submitted: false
  })

  useEffect(() => {
    loadFinalReport()
  }, [user?.id])

  const loadFinalReport = async () => {
    if (!user?.id) return
    
    try {
      setLoading(true)
      const result = await FinalReports.getMyFinalReport()
      if (result?.data) {
        setFinalReport(result.data)
        setReportData(result.data)
        setSubmissionType(result.data.submission_type || 'write')
      }
    } catch (error) {
      // No final report exists yet
      console.log('No final report found, starting fresh')
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      
      const dataToSave = {
        ...reportData,
        submission_type: submissionType,
        student_id: user?.id || "",
        character_count: reportData.content_plain?.length || 0,
        word_count: reportData.content_plain?.split(' ').filter(word => word.length > 0).length || 0
      }

      if (finalReport?.id) {
        await FinalReports.updateFinalReport(finalReport.id, dataToSave)
      } else {
        const result = await FinalReports.createFinalReport(dataToSave as FinalReport)
        if (result?.data) {
          setFinalReport(result.data)
        }
      }
      
      await loadFinalReport()
    } catch (error) {
      console.error('Failed to save final report:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleSubmit = async () => {
    try {
      setSaving(true)
      
      const dataToSubmit = {
        ...reportData,
        submission_type: submissionType,
        student_id: user?.id || "",
        status: 'submitted',
        is_submitted: true,
        submitted_at: new Date().toISOString(),
        character_count: reportData.content_plain?.length || 0,
        word_count: reportData.content_plain?.split(' ').filter(word => word.length > 0).length || 0
      }

      // Debug: Log what we're about to submit
      console.log('Frontend submitting data:', {
        title: dataToSubmit.title,
        submission_type: dataToSubmit.submission_type,
        content_html: dataToSubmit.content_html ? `${dataToSubmit.content_html.substring(0, 100)}...` : 'NULL',
        content_plain: dataToSubmit.content_plain ? `${dataToSubmit.content_plain.substring(0, 100)}...` : 'NULL',
        character_count: dataToSubmit.character_count,
        word_count: dataToSubmit.word_count,
        status: dataToSubmit.status,
        is_submitted: dataToSubmit.is_submitted
      })

      if (finalReport?.id) {
        await FinalReports.updateFinalReport(finalReport.id, dataToSubmit)
      } else {
        const result = await FinalReports.createFinalReport(dataToSubmit as FinalReport)
        if (result?.data) {
          setFinalReport(result.data)
        }
      }
      
      await loadFinalReport()
    } catch (error) {
      console.error('Failed to submit final report:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file size (10MB limit)
    if (file.size > 10 * 1024 * 1024) {
      toast.error('File size must be less than 10MB')
      return
    }

    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
    if (!allowedTypes.includes(file.type)) {
      toast.error('Only PDF, DOC, and DOCX files are allowed')
      return
    }

    try {
      setSaving(true)

      // For now, we'll store the file info locally
      // In a real implementation, you'd upload to a file storage service
      const fileUrl = URL.createObjectURL(file)

      updateReportData('file_url', fileUrl)
      updateReportData('file_name', file.name)
      updateReportData('file_size', file.size)
      updateReportData('file_type', file.type)

      toast.success('File uploaded successfully')
    } catch (error) {
      console.error('Failed to upload file:', error)
      toast.error('Failed to upload file')
    } finally {
      setSaving(false)
    }
  }

  const updateReportData = (field: keyof FinalReport, value: any) => {
    setReportData(prev => ({ ...prev, [field]: value }))
  }

  const isSubmitted = finalReport?.is_submitted || false
  const canEdit = !isSubmitted

  if (loading) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="flex flex-col gap-4 px-4 lg:px-6">
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading final report...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="flex flex-1 flex-col">
        <div className="@container/main flex flex-1 flex-col gap-2">
          <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
            <div className="flex flex-col gap-4 px-4 lg:px-6">
              <div className="flex items-center justify-center h-64">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
                  <p className="text-muted-foreground">Loading your final report...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">
            
            {/* Header */}
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-2">
                <h1 className="text-2xl font-bold">Final Internship Report</h1>
                <p className="text-muted-foreground">
                  {isSubmitted
                    ? "Your submitted final internship report"
                    : "Submit your comprehensive internship report"}
                </p>
              </div>
              <div className="flex items-center gap-2">
                {finalReport && (
                  <Badge variant={isSubmitted ? "default" : "secondary"}>
                    {isSubmitted ? "Submitted" : "Draft"}
                  </Badge>
                )}
                {canEdit && (
                  <>
                    <Button
                      variant="outline"
                      onClick={handleSave}
                      disabled={saving}
                      className="flex items-center gap-2"
                    >
                      <Save className="h-4 w-4" />
                      Save Draft
                    </Button>
                    <Button
                      onClick={handleSubmit}
                      disabled={saving ||
                        (submissionType === 'write' && (!reportData.content_plain || reportData.content_plain.length < 500)) ||
                        (submissionType === 'upload' && !reportData.file_url)}
                      className="flex items-center gap-2"
                    >
                      <Send className="h-4 w-4" />
                      Submit Report
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Status Card */}
            {finalReport && (
              <Card>
                <CardContent className="pt-6">
                  <div className="flex items-center gap-4">
                    {isSubmitted ? (
                      <CheckCircle className="h-8 w-8 text-green-500" />
                    ) : (
                      <AlertCircle className="h-8 w-8 text-orange-500" />
                    )}
                    <div>
                      <h3 className="font-medium">
                        {isSubmitted ? 'Report Submitted Successfully' : 'Report in Progress'}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {isSubmitted 
                          ? `Submitted on ${new Date(finalReport.submitted_at!).toLocaleDateString()}`
                          : 'Continue working on your final report'
                        }
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Main Content Area */}
            {isSubmitted ? (
              /* Submitted Report Display */
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="flex items-center gap-2">
                        <CheckCircle className="h-5 w-5 text-green-600" />
                        Submitted Report
                      </CardTitle>
                      <p className="text-sm text-muted-foreground mt-1">
                        Submitted on {finalReport?.submitted_at ? new Date(finalReport.submitted_at).toLocaleDateString() : 'Unknown date'}
                      </p>
                    </div>
                    <Badge variant="default" className="bg-green-600">
                      Submitted
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {/* Report Title */}
                    <div>
                      <h3 className="text-lg font-semibold mb-3">{finalReport?.title || 'Final Internship Report'}</h3>
                    </div>

                    {/* Report Content */}
                    {submissionType === 'write' ? (
                      <div>
                        <h4 className="font-medium mb-3 text-sm text-muted-foreground uppercase tracking-wide">Report Content</h4>
                        <div className="p-6 bg-muted/30 rounded-lg border">
                          {finalReport?.content_html ? (
                            <div
                              className="prose prose-sm max-w-none prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground prose-ul:text-foreground prose-ol:text-foreground"
                              dangerouslySetInnerHTML={{ __html: finalReport.content_html }}
                            />
                          ) : (
                            <p className="text-sm whitespace-pre-wrap text-muted-foreground">
                              {finalReport?.content_plain || 'No content available'}
                            </p>
                          )}
                        </div>

                        {/* Report Statistics */}
                        <div className="flex items-center gap-4 mt-4 text-xs text-muted-foreground">
                          <span>{finalReport?.character_count || 0} characters</span>
                          <span>•</span>
                          <span>{finalReport?.word_count || 0} words</span>
                        </div>
                      </div>
                    ) : (
                      /* File Upload Display */
                      <div>
                        <h4 className="font-medium mb-3 text-sm text-muted-foreground uppercase tracking-wide">Uploaded File</h4>
                        {finalReport?.file_name ? (
                          <div className="flex items-center gap-3 p-4 bg-muted/30 rounded-lg border">
                            <FileText className="h-8 w-8 text-blue-600" />
                            <div className="flex-1">
                              <div className="font-medium">{finalReport.file_name}</div>
                              <div className="text-sm text-muted-foreground">
                                {finalReport.file_size ? `${Math.round(finalReport.file_size / 1024)} KB` : 'Unknown size'}
                              </div>
                            </div>
                            {finalReport.file_url && (
                              <Button variant="outline" size="sm" asChild>
                                <a href={finalReport.file_url} download target="_blank" rel="noopener noreferrer">
                                  <Download className="h-4 w-4 mr-2" />
                                  Download
                                </a>
                              </Button>
                            )}
                          </div>
                        ) : (
                          <p className="text-sm text-muted-foreground">No file information available</p>
                        )}
                      </div>
                    )}

                    {/* Supervisor Feedback */}
                    {finalReport?.supervisor_comments && (
                      <div>
                        <h4 className="font-medium mb-3 text-sm text-muted-foreground uppercase tracking-wide">Supervisor Feedback</h4>
                        <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg border border-blue-200 dark:border-blue-800">
                          <p className="text-sm mb-3">{finalReport.supervisor_comments}</p>
                          {finalReport.supervisor_rating && (
                            <div className="flex items-center gap-2">
                              <span className="text-sm font-medium">Rating:</span>
                              <Badge variant="secondary">{finalReport.supervisor_rating}/10</Badge>
                            </div>
                          )}
                          {finalReport.grade && (
                            <div className="flex items-center gap-2 mt-2">
                              <span className="text-sm font-medium">Grade:</span>
                              <Badge variant="outline">{finalReport.grade}</Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ) : (
              /* Editor Mode for Draft/New Reports */
              <>
                {/* Submission Type Selection */}
                <Card>
                  <CardHeader>
                    <CardTitle>Submission Method</CardTitle>
                    <p className="text-sm text-muted-foreground">Choose how you want to submit your final report</p>
                  </CardHeader>
                  <CardContent>
                    <Tabs value={submissionType} onValueChange={(value) => setSubmissionType(value as 'write' | 'upload')}>
                      <TabsList className="grid w-full grid-cols-2">
                        <TabsTrigger value="write" className="flex items-center gap-2">
                          <FileText className="h-4 w-4" />
                          Write Online
                        </TabsTrigger>
                        <TabsTrigger value="upload" className="flex items-center gap-2">
                          <Upload className="h-4 w-4" />
                          Upload File
                        </TabsTrigger>
                      </TabsList>
                    </Tabs>
                  </CardContent>
                </Card>

                {/* Content Editor */}
                {submissionType === 'write' ? (
              <Card>
                <CardHeader>
                  <CardTitle>Report Content</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-2">
                    <Label htmlFor="title">Report Title</Label>
                    <Input
                      id="title"
                      value={reportData.title || ''}
                      onChange={(e) => updateReportData('title', e.target.value)}
                      disabled={!canEdit}
                      placeholder="Final Internship Report"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="content">
                      Report Content <span className="text-destructive">*</span>
                    </Label>
                    <RichTextEditor
                      content={reportData.content_html || ''}
                      onChange={(html, plainText) => {
                        updateReportData('content_html', html)
                        updateReportData('content_plain', plainText)
                      }}
                      disabled={!canEdit}
                      placeholder="Write your comprehensive final report here. Include your experiences, learnings, achievements, and reflections from your internship period. Minimum 500 characters required."
                      minHeight="400px"
                      characterLimit={10000}
                    />
                    <div className="text-xs text-muted-foreground">
                      {(reportData.content_plain || '').length} / 500 characters minimum
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Upload Report File</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
                      <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                      <h3 className="text-lg font-medium mb-2">Upload your final report</h3>
                      <p className="text-muted-foreground mb-4">
                        Supported formats: PDF, DOC, DOCX (Max 10MB)
                      </p>
                      <input
                        type="file"
                        accept=".pdf,.doc,.docx"
                        onChange={handleFileUpload}
                        disabled={!canEdit || saving}
                        className="hidden"
                        id="file-upload"
                      />
                      <label htmlFor="file-upload">
                        <Button variant="outline" disabled={!canEdit || saving} asChild>
                          <span>Choose File</span>
                        </Button>
                      </label>
                    </div>
                    
                    {(finalReport?.file_name || reportData.file_name) && (
                      <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                        <FileText className="h-4 w-4" />
                        <div className="flex-1">
                          <div className="font-medium">{finalReport?.file_name || reportData.file_name}</div>
                          <div className="text-xs text-muted-foreground">
                            {finalReport?.file_size || reportData.file_size ?
                              `${Math.round((finalReport?.file_size || reportData.file_size || 0) / 1024)} KB` :
                              ''}
                          </div>
                        </div>
                        {finalReport?.file_url && (
                          <Button variant="ghost" size="sm" asChild>
                            <a href={finalReport.file_url} download target="_blank" rel="noopener noreferrer">
                              <Download className="h-4 w-4" />
                            </a>
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            )}
              </>
            )}

          </div>
        </div>
      </div>
    </div>
  )
}
