import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

interface FinalReportFeedback {
    final_report_id: string;
    supervisor_comments?: string;
    supervisor_rating?: number;
    grade?: string;
}

export const useAddFinalReportFeedback = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation<any, Error, FinalReportFeedback>({ 
        mutationFn: async ({ final_report_id, ...feedback }: FinalReportFeedback) => {
            const res = await Supervisor.addFinalReportFeedback(final_report_id, feedback);
            return res;
        },
        onSuccess: (data, variables) => {
            // Invalidate related queries
            queryClient.invalidateQueries({ queryKey: ["supervisor-student-details"] });
            queryClient.invalidateQueries({ queryKey: ["supervisor-assigned-students"] });
            queryClient.invalidateQueries({ queryKey: ["supervisor-dashboard-stats"] });
        },
        onError: (error) => {
            console.error('Add final report feedback error:', error);
        },
    });

    return mutation;
};
