import axios from "axios";
import { toast } from "sonner";

export class Supervisor {
    public static api_url = process.env.NEXT_PUBLIC_API_URL;

    public static async init(){
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.get(`${this.api_url}/other/supervisors/checkup`, {
              headers: {
                'Authorization': token
              }
            });
    
            if (response.status === 200) {
                return response.data.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }

    public static async index() {
        try {
          const response = await axios.get(`${this.api_url}/other/supervisors`);
    
          if (response.status === 200) {
            return response.data;
          }
        } catch (error: any) {
          throw error.response.data;
        }
    }
      
    public static async show(id: string) {
        try {
        const response = await axios.get(`${this.api_url}/other/supervisors/${id}`);
    
        if (response.status === 200) {
            return response.data;
        }
        } catch (error: any) {
        throw error.response.data;
        }
    }

    public static async create(first_name: string , last_name: string , email: string , phone: string , ipt_id: string) {
        const token = localStorage.getItem('iptms_token');
        try {
            const response = await axios.post(`${this.api_url}/other/supervisors`,{
                first_name , last_name , email , phone, ipt_id
            },{
                headers: {
                    'Authorization': token
                }
            }); 
    
            if (response.status === 200) {
                toast.success(response.data.message);
                return response.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }

    public static async update(first_name: string , last_name: string , email: string , phone: string ,ipt_id : string, supervisor_id: string) {
        try {
            const response = await axios.put(`${this.api_url}/other/supervisors/${supervisor_id}`,{
                first_name , last_name , email , phone , ipt_id 
            }); 
    
            if (response.status === 200) {
                toast.success(response.data.message);
                return response.data;
            }
        } catch (error: any) {
            throw error.response.data;
        }
    }

    public static async changePassword(password:string){
        const token = localStorage.getItem('iptms_token');

        try {
            const response = await axios.post(`${this.api_url}/other/supervisors/changePassword`, {
                password
            },{
                headers: {
                    'Authorization': token
                }
            }); 
    
            if (response.status === 200) {
                toast.success(response.data.message);
                return response.data;
            }
        } catch (error: any) {
            throw error.response.data;  
        }
    }

    public static async login(email: string, password: string) {
        try {
            const response = await axios.post(`${this.api_url}/other/supervisors/login`, {
                email,
                password,
            });
    
            if (response.status === 200) {
                toast.success(response.data.message);
                return response.data;
            }
        }
        catch (error: any) {
            throw error.response.data;
        }
    }

    public static async destroy(id: string) {
        try {
        const response = await axios.delete(`${this.api_url}/other/supervisors/${id}`);

        if (response.status === 200) {
            toast.success(response.data.message);
            return response.data;
        }
        } catch (error: any) {
            throw error.response.data;
        }
    }

    // Get assigned students for current supervisor
    public static async getAssignedStudents() {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.get(`${this.api_url}/other/supervisors/students`, {
                headers: {
                    'Authorization': token
                }
            });

            if (response.status === 200) {
                return response.data;
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || "Failed to fetch assigned students");
            throw error.response?.data;
        }
    }

    // Get supervisor dashboard statistics
    public static async getDashboardStats() {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.get(`${this.api_url}/other/supervisors/dashboard/stats`, {
                headers: {
                    'Authorization': token
                }
            });

            if (response.status === 200) {
                return response.data;
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || "Failed to fetch dashboard statistics");
            throw error.response?.data;
        }
    }

    // Get detailed student information
    public static async getStudentDetails(student_id: string) {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.get(`${this.api_url}/other/supervisors/students/${student_id}`, {
                headers: {
                    'Authorization': token
                }
            });


            if (response.status === 200) {
                return response.data;
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || "Failed to fetch student details");
            throw error.response?.data;
        }
    }

    // Add feedback to daily log entry
    public static async addDailyLogFeedback(daily_log_id: string, supervisor_feedback: string) {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.post(`${this.api_url}/other/supervisors/daily-logs/${daily_log_id}/feedback`, {
                supervisor_feedback
            }, {
                headers: {
                    'Authorization': token,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 200) {
                toast.success("Feedback added successfully");
                return response.data;
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || "Failed to add feedback");
            throw error.response?.data;
        }
    }

    // Add feedback to final report
    public static async addFinalReportFeedback(final_report_id: string, feedback: {
        supervisor_comments?: string;
        supervisor_rating?: number;
        grade?: string;
    }) {
        try {
            const token = localStorage.getItem('iptms_token');
            const response = await axios.post(`${this.api_url}/other/supervisors/final-reports/${final_report_id}/feedback`, feedback, {
                headers: {
                    'Authorization': token,
                    'Content-Type': 'application/json'
                }
            });

            if (response.status === 200) {
                toast.success("Feedback added successfully");
                return response.data;
            }
        } catch (error: any) {
            toast.error(error.response?.data?.message || "Failed to add feedback");
            throw error.response?.data;
        }
    }
}