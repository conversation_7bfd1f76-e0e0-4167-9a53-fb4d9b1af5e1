import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Program, ProgramData } from "../server/Program";

export const useUpdateProgram = () => {
  const queryClient = useQueryClient();

  const mutation = useMutation<any, Error, { id: string; data: ProgramData }>({ 
    mutationFn: async ({ id, data }: { id: string; data: ProgramData }) => {
      const res = await Program.update(id, data);
      return res;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
    },
    onError: (error) => {
      console.error('Update program error:', error);
    },
  });

  return mutation;
};
