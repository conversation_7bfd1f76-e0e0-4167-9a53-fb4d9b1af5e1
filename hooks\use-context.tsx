import { ArrivalNote } from '@/features/arrrival_note/server/ArrivalNote';
import { Auth } from '@/features/auth/server/Auth';
import { Supervisor } from '@/features/supervisors/server/Supervisor';
import { ReactNode } from 'react';
import React, { createContext, useContext, useState, useEffect } from 'react';

export interface IptData {
  data: {
    id: string;
  };
}

interface ContextType {
  isAuthenticated: boolean;
  setAuthentication: (value: boolean) => void;
  checkAuthentication: () => void;
  handleLogin: (token: any) => void;
  user: any;
  setUser: (user: any) => void;
  loading: boolean;
  handleLogout: () => void;
  iptId: string | null;
  changeIptId: (data: IptData | null) => void;
  isRegistered: boolean,
  setIsRegistered: (value: boolean) => void,
  fetchArrivalNotesData: () => void,
  needsPasswordChange: boolean,
  hasSubmitArrivalNote: boolean,
}

const globalManager = createContext<ContextType>({
  isAuthenticated: false,
  setAuthentication: () => {},
  checkAuthentication: () => {},
  handleLogin: () => {},
  user: null,
  setUser: () => {},
  loading: true,  
  handleLogout: () => {},
  iptId: null,
  changeIptId: () => {},
  isRegistered: false,
  setIsRegistered: () => {},
  fetchArrivalNotesData: () => {},
  needsPasswordChange: false,
  hasSubmitArrivalNote: false,
});

export const GlobalManagerProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [user, setUser] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true); 
  const [iptId, setIptId] = useState<string | null>(null);
  const [isRegistered,setIsRegistered] = useState<boolean>(false);
  const [hasSubmitArrivalNote, setHasSubmitArrivalNote] = useState<boolean>(false);
  const [needsPasswordChange, setNeedsPasswordChange] = useState<boolean>(false);

  // console.log('the current ipt ==>', iptId);
  // console.log('the current user ==>',user)
  const fetchArrivalNotesData = async () => {
    const value = await ArrivalNote.init();
    setHasSubmitArrivalNote(value);
  };

  useEffect(() => {
    const supa = async () => {
      const res = await Supervisor.getAssignedStudents();
      console.log('context', res);
    }
    if(isAuthenticated){

      fetchArrivalNotesData();
      supa();
    }
  }, [user]);

  useEffect(() => {
    const fetchData = async () => {
      const value = await Supervisor.init();
      setNeedsPasswordChange(value);
    };

    if(isAuthenticated){

      fetchData();
    }
  }, [user]);

  const changeIptId = (data: IptData | null) => {
    setIptId(data?.data?.id ?? null);
  };

  const setAuthentication = (value: boolean) => {
    setIsAuthenticated(value);
  };

  const handleLogin = (data: any) => {
    localStorage.setItem('iptms_token', data.token);
    checkAuthentication(); 
  };

  const checkAuthentication = async () => {
    const token = localStorage.getItem('iptms_token');
    setLoading(true);

    if (token) {
      try {
        const response = await Auth.getUser(token);

        if (response.success) {
          setUser(response.data);
          setIsAuthenticated(true);
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("Failed to get user:", error);
        setIsAuthenticated(false);
      }
    } else {
      setIsAuthenticated(false);
    }

    setTimeout(() => {
      setLoading(false);
    }, 1000);
  };

  useEffect(() => {
    checkAuthentication(); 
  }, []);

  const handleLogout = () => {
    localStorage.removeItem('iptms_token');
    setIsAuthenticated(false);
    setUser(null);
  };

  return (
    <globalManager.Provider
      value={{
        isAuthenticated,
        needsPasswordChange,
        setAuthentication,
        checkAuthentication,
        handleLogin,
        user,
        setUser,
        loading,
        handleLogout,
        iptId,
        changeIptId,
        isRegistered,
        setIsRegistered,
        hasSubmitArrivalNote,
        fetchArrivalNotesData
      }}
    >
      {children}
    </globalManager.Provider>
  );
};

export const useGlobalManager = () => useContext(globalManager);
