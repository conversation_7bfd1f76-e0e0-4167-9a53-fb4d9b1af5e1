import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";
import { useGlobalManager } from "@/hooks/use-context";

export const useAddDailyLogFeedback = () => {
  const queryClient = useQueryClient();
  const { iptId } = useGlobalManager();

  return useMutation({
    mutationFn: async ({ daily_log_id, supervisor_feedback }: {
      daily_log_id: string;
      supervisor_feedback: string
    }) => {
      return await Supervisor.addDailyLogFeedback(daily_log_id, supervisor_feedback);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries with IPT context
      queryClient.invalidateQueries({ queryKey: ["supervisor", "student", iptId] });
      queryClient.invalidateQueries({ queryKey: ["supervisor", "dashboard", iptId] });
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to add feedback");
    },
  });
};

export const useAddWeeklyReportFeedback = () => {
  const queryClient = useQueryClient();
  const { iptId } = useGlobalManager();

  return useMutation({
    mutationFn: async ({
      weekly_report_id,
      feedback
    }: {
      weekly_report_id: string;
      feedback: {
        supervisor_comments: string;
        supervisor_rating?: number;
      }
    }) => {
      return await Supervisor.addWeeklyReportFeedback(weekly_report_id, feedback);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries with IPT context
      queryClient.invalidateQueries({ queryKey: ["supervisor", "student", iptId] });
      queryClient.invalidateQueries({ queryKey: ["supervisor", "dashboard", iptId] });
      toast.success("Weekly report feedback added successfully");
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to add feedback");
    },
  });
};

export const useAddFinalReportFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      final_report_id,
      feedback
    }: {
      final_report_id: string;
      feedback: {
        supervisor_comments?: string;
        supervisor_rating?: number;
        grade?: string;
      }
    }) => {
      return await Supervisor.addFinalReportFeedback(final_report_id, feedback);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["supervisor", "student"] });
      queryClient.invalidateQueries({ queryKey: ["supervisor", "dashboard"] });
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to add feedback");
    },
  });
};
