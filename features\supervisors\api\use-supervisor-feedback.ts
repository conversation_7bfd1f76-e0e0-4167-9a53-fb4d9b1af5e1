import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Supervisor } from "../server/Supervisor";

export const useAddDailyLogFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ daily_log_id, supervisor_feedback }: { 
      daily_log_id: string; 
      supervisor_feedback: string 
    }) => {
      return await Supervisor.addDailyLogFeedback(daily_log_id, supervisor_feedback);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["supervisor", "student"] });
      queryClient.invalidateQueries({ queryKey: ["supervisor", "dashboard"] });
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to add feedback");
    },
  });
};

export const useAddFinalReportFeedback = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ 
      final_report_id, 
      feedback 
    }: { 
      final_report_id: string; 
      feedback: {
        supervisor_comments?: string;
        supervisor_rating?: number;
        grade?: string;
      }
    }) => {
      return await Supervisor.addFinalReportFeedback(final_report_id, feedback);
    },
    onSuccess: () => {
      // Invalidate and refetch relevant queries
      queryClient.invalidateQueries({ queryKey: ["supervisor", "student"] });
      queryClient.invalidateQueries({ queryKey: ["supervisor", "dashboard"] });
    },
    onError: (error: any) => {
      toast.error(error?.message || "Failed to add feedback");
    },
  });
};
