import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Student } from "../server/Student";
import { useGlobalManager } from "@/hooks/use-context";

export const useFetchStudents = () => {
    const { iptId } = useGlobalManager();

    const query = useQuery({
        queryKey: ["students", iptId],
        queryFn: async () => {
            try {
                const res = await Student.index();
                if (res.success) {
                    // console.log(res.data)
                    return res.data;
                }
                return [];
            } catch (error: any) {
                toast.error(error.message);
                return [];
            }
        },
        enabled: !!iptId, // Only run query when IPT is selected
    });
    return query;
}
