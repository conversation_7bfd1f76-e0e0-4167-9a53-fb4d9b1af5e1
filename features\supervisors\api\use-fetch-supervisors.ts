import { useQuery } from "@tanstack/react-query"
import { Supervisor } from "../server/Supervisor";
import { toast } from "sonner";
import { useGlobalManager } from "@/hooks/use-context";

export const useFetchSupervisor = () =>{
    const { iptId } = useGlobalManager();

    const query = useQuery({
        queryKey:['supervisors', iptId],
        queryFn: async () => {
            try {
                const res = await Supervisor.index();

                if(res.success){

                    return res.data;
                }
                return []
            } catch (error: any) {
                toast.error(error.message)
                return []
            }
        },
        enabled: !!iptId, // Only run query when IPT is selected
    })

    return query;
}