"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { 
  <PERSON>, 
  FileText, 
  MessageSquare, 
  Clock, 
  Alert<PERSON>riangle,
  TrendingUp 
} from "lucide-react"

interface SupervisorDashboardStats {
  total_students: number;
  students_with_logs_this_week: number;
  students_logged_this_week: number;
  students_with_submitted_reports: number;
  students_with_pending_reports: number;
  students_with_final_reports: number;
  students_with_supervisor_feedback: number;
}

interface SupervisorQuickActionsProps {
  stats?: SupervisorDashboardStats
}

export const SupervisorQuickActions = ({ stats }: SupervisorQuickActionsProps) => {
  const pendingReports = stats?.students_with_pending_reports || 0
  const activeStudents = stats?.students_logged_this_week || 0
  const totalStudents = stats?.total_students || 0

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {/* View All Students */}
      <Card className="hover:bg-accent cursor-pointer transition-colors">
        <Link href="/supervisor/students">
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 mr-4 text-primary" />
            <div className="flex-1">
              <h3 className="font-medium">View All Students</h3>
              <p className="text-sm text-muted-foreground">
                Manage your {totalStudents} assigned students
              </p>
            </div>
          </CardContent>
        </Link>
      </Card>

      {/* Pending Reports */}
      {pendingReports > 0 && (
        <Card className="hover:bg-accent cursor-pointer transition-colors border-amber-200">
          <Link href="/supervisor/students?filter=pending">
            <CardContent className="flex items-center p-6">
              <AlertTriangle className="h-8 w-8 mr-4 text-amber-500" />
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium">Pending Reports</h3>
                  <Badge variant="secondary" className="bg-amber-100 text-amber-700">
                    {pendingReports}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground">
                  Students need your attention
                </p>
              </div>
            </CardContent>
          </Link>
        </Card>
      )}

      {/* Weekly Activity */}
      <Card className="hover:bg-accent cursor-pointer transition-colors">
        <CardContent className="flex items-center p-6">
          <TrendingUp className="h-8 w-8 mr-4 text-green-500" />
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <h3 className="font-medium">This Week's Activity</h3>
              <Badge variant="outline" className="text-green-600">
                {activeStudents}/{totalStudents}
              </Badge>
            </div>
            <p className="text-sm text-muted-foreground">
              Students logged activities
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Reports Review */}
      <Card className="hover:bg-accent cursor-pointer transition-colors">
        <Link href="/supervisor/reports">
          <CardContent className="flex items-center p-6">
            <FileText className="h-8 w-8 mr-4 text-blue-500" />
            <div className="flex-1">
              <h3 className="font-medium">Review Reports</h3>
              <p className="text-sm text-muted-foreground">
                Weekly and final reports awaiting review
              </p>
            </div>
          </CardContent>
        </Link>
      </Card>

      {/* Feedback & Communication */}
      <Card className="hover:bg-accent cursor-pointer transition-colors">
        <Link href="/supervisor/feedback">
          <CardContent className="flex items-center p-6">
            <MessageSquare className="h-8 w-8 mr-4 text-purple-500" />
            <div className="flex-1">
              <h3 className="font-medium">Give Feedback</h3>
              <p className="text-sm text-muted-foreground">
                Provide feedback on student work
              </p>
            </div>
          </CardContent>
        </Link>
      </Card>

      {/* Schedule Check-in */}
      <Card className="hover:bg-accent cursor-pointer transition-colors">
        <CardContent className="flex items-center p-6">
          <Clock className="h-8 w-8 mr-4 text-indigo-500" />
          <div className="flex-1">
            <h3 className="font-medium">Schedule Check-in</h3>
            <p className="text-sm text-muted-foreground">
              Plan meetings with students
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
