"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { DottedSeparator } from "@/components/dotted-separator"
import { useFetchSupervisorDashboardStats } from "../api/use-fetch-supervisor-dashboard-stats"
import { useFetchAssignedStudents } from "../api/use-fetch-assigned-students"
import { Users, BookOpen, FileText, Clock, CheckCircle2, AlertCircle, MessageSquare, RefreshCw } from "lucide-react"
import { SupervisorStudentsList } from "./supervisor-students-list"
import { SupervisorQuickActions } from "./supervisor-quick-actions"
import { Button } from "@/components/ui/button"

export const SupervisorDashboard = () => {
  const { 
    data: stats, 
    isLoading: statsLoading, 
    error: statsError,
    refetch: refetchStats 
  } = useFetchSupervisorDashboardStats()
  

  const { 
    data: students, 
    isLoading: studentsLoading, 
    error: studentsError,
    refetch: refetchStudents 
  } = useFetchAssignedStudents()

  // Handle loading state
  if (statsLoading || studentsLoading) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  // Handle error state
  if (statsError || studentsError) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load dashboard data. Please try again.
          </AlertDescription>
        </Alert>
        <Button 
          onClick={() => {
            refetchStats()
            refetchStudents()
          }}
          variant="outline"
          size="sm"
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </div>
    )
  }

  const completionRate = stats?.total_students 
    ? Math.round((stats.students_with_submitted_reports / stats.total_students) * 100)
    : 0

  const activityRate = stats?.total_students
    ? Math.round((stats.students_logged_this_week / stats.total_students) * 100)
    : 0

  return (
    <div className="flex flex-col gap-6">
      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Students</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.total_students || 0}</div>
            <p className="text-xs text-muted-foreground">
              Assigned to you
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Activity</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.students_logged_this_week || 0}</div>
            <p className="text-xs text-muted-foreground">
              {activityRate}% active this week
            </p>
            <Progress value={activityRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Weekly Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.students_with_submitted_reports || 0}</div>
            <p className="text-xs text-muted-foreground">
              {completionRate}% completion rate
            </p>
            <Progress value={completionRate} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Final Reports</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.students_with_final_reports || 0}</div>
            <p className="text-xs text-muted-foreground">
              Students completed
            </p>
          </CardContent>
        </Card>
      </div>

      <DottedSeparator />

      {/* Quick Actions */}
      <div>
        <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
        <SupervisorQuickActions stats={stats} />
      </div>

      <DottedSeparator />

      {/* Recent Student Activity */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold">Your Students</h2>
          <Badge variant="outline">
            {students?.length || 0} Total
          </Badge>
        </div>
        
        <SupervisorStudentsList students={students || []} />
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Progress Overview</CardTitle>
          <CardDescription>
            Track your students' overall progress and areas needing attention
          </CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4 md:grid-cols-3">
          <div className="flex items-center space-x-4 p-4 bg-green-50 rounded-lg">
            <CheckCircle2 className="h-8 w-8 text-green-500" />
            <div>
              <h4 className="font-medium text-green-700">On Track</h4>
              <p className="text-sm text-green-600">
                {stats?.students_with_submitted_reports || 0} students submitting regularly
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 p-4 bg-amber-50 rounded-lg">
            <AlertCircle className="h-8 w-8 text-amber-500" />
            <div>
              <h4 className="font-medium text-amber-700">Needs Attention</h4>
              <p className="text-sm text-amber-600">
                {stats?.students_with_pending_reports || 0} students with pending reports
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-4 p-4 bg-blue-50 rounded-lg">
            <MessageSquare className="h-8 w-8 text-blue-500" />
            <div>
              <h4 className="font-medium text-blue-700">Feedback Given</h4>
              <p className="text-sm text-blue-600">
                {stats?.students_with_supervisor_feedback || 0} students received feedback
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
