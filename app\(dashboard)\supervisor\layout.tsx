"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useGlobalManager } from "@/hooks/use-context"

export default function SupervisorLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { user, isAuthenticated } = useGlobalManager()

  const router = useRouter()

  useEffect(() => {
    // Check if user is authenticated and has supervisor role
    if (!isAuthenticated) {
      router.push("/")
      return
    }

    // Ensure the user has supervisor role (role_name should be 'supervisor')
    if (user && user.role !== "supervisor") {
      router.push("/")
      return
    }
  }, [isAuthenticated, user, router])

  // Show loading if authentication is still being checked
  if (!isAuthenticated || !user) {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  // Show unauthorized if user is not a supervisor
  if (user.role !== "supervisor") {
    return (
      <div className="flex items-center justify-center h-40">
        <div className="text-center">
          <p className="text-muted-foreground">Unauthorized access</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
