"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Search, Users, Eye, BookOpen, FileText } from "lucide-react"
import { useFetchAssignedStudents } from "@/features/supervisors/api/use-fetch-assigned-students"
import { useFetchStudentDetails } from "@/features/supervisors/api/use-fetch-student-details"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"

import { StudentDetailsTabs } from "@/features/supervisors/components/student-details-tabs"

const SupervisorStudentsPage = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedStudentId, setSelectedStudentId] = useState<string | null>(null)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)

  // Hooks
  const { data: students = [], isLoading } = useFetchAssignedStudents()
  const { data: studentDetails, isLoading: isLoadingDetails } = useFetchStudentDetails(selectedStudentId || "")
  

  console.log('student' , students)
  // Filter students based on search query
  const filteredStudents = students.filter((student: any) =>
    student.student_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    student.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (student.program_name && student.program_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (student.reg_no && student.reg_no.toLowerCase().includes(searchQuery.toLowerCase()))
  )

  // Handle view student details
  const handleViewStudent = (studentId: string) => {
    setSelectedStudentId(studentId)
    setIsDetailsModalOpen(true)
  }

  // Close modal
  const closeModal = () => {
    setIsDetailsModalOpen(false)
    setSelectedStudentId(null)
  }

  return (
    <div className="flex flex-1 flex-col">
      <div className="@container/main flex flex-1 flex-col gap-2">
        <div className="flex flex-col gap-4 py-4 md:gap-6 md:py-6">
          <div className="flex flex-col gap-4 px-4 lg:px-6">

            {/* Header */}
            <div className="flex flex-col gap-2">
              <h1 className="text-2xl font-bold">Your Students</h1>
              <p className="text-muted-foreground">
                Manage and monitor your assigned students' progress
              </p>
            </div>

            {/* Statistics Cards */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Total Students</CardTitle>
                  <Users className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-600">
                    {students.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Assigned to you
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Active Students</CardTitle>
                  <BookOpen className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-600">
                    {students.length}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Currently active
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">Programs</CardTitle>
                  <FileText className="h-4 w-4 text-muted-foreground" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-600">
                    {new Set(students.map((s: any) => s.program_name).filter(Boolean)).size}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Different programs
                  </p>
                </CardContent>
              </Card>
            </div>

            {/* Search Bar */}
            <div className="flex items-center gap-2">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search students..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-8"
                />
              </div>
            </div>

            {/* Students Table */}
            <Card>
              <CardHeader>
                <CardTitle>Students List</CardTitle>
                <CardDescription>
                  All students assigned to your supervision
                </CardDescription>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <div className="flex items-center justify-center h-32">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  </div>
                ) : (
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Registration No.</TableHead>
                        <TableHead>Program</TableHead>
                        <TableHead>Class</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={6} className="text-center py-8">
                            <div className="text-muted-foreground">
                              {searchQuery ? "No students found matching your search" : "No students assigned yet"}
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredStudents.map((student: any) => (
                          <TableRow key={student.student_id}>
                            <TableCell>
                              <div className="flex items-center space-x-3">
                                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                                  <span className="text-xs font-medium text-blue-600">
                                    {student.first_name?.[0]}{student.last_name?.[0]}
                                  </span>
                                </div>
                                <div>
                                  <p className="font-medium">{student.student_name}</p>
                                  <p className="text-sm text-muted-foreground">{student.email}</p>
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <Badge variant="outline">
                                {student.reg_no || 'N/A'}
                              </Badge>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm">
                                {student.program_name || 'N/A'}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm">
                                {student.class || 'N/A'}
                              </span>
                            </TableCell>
                            <TableCell>
                              <span className="text-sm text-muted-foreground">
                                {student.phone || 'N/A'}
                              </span>
                            </TableCell>
                            <TableCell className="text-right">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleViewStudent(student.student_id)}
                                className="flex items-center gap-2"
                              >
                                <Eye className="h-4 w-4" />
                                View
                              </Button>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                )}
              </CardContent>
            </Card>

          </div>
        </div>
      </div>

      {/* Student Details Modal */}
      <Dialog open={isDetailsModalOpen} onOpenChange={setIsDetailsModalOpen}>
        <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Student Details
            </DialogTitle>
            <DialogDescription>
              {studentDetails?.student ?
                `Viewing details for ${studentDetails.student.student_name}` :
                'Loading student details...'
              }
            </DialogDescription>
          </DialogHeader>

          {isLoadingDetails ? (
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : studentDetails ? (
            <StudentDetailsTabs
              studentDetails={studentDetails}
              onClose={closeModal}
            />
          ) : (
            <div className="text-center py-8">
              <p className="text-muted-foreground">Failed to load student details</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default SupervisorStudentsPage
