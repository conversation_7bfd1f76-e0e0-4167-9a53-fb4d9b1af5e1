"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { 
  BookOpen, 
  FileText, 
  MessageSquare, 
  Star,
  Calendar,
  Clock,
  User,
  Mail,
  Phone,
  GraduationCap,
  Building
} from "lucide-react"
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useAddDailyLogFeedback, useAddFinalReportFeedback } from "../api/use-supervisor-feedback"
import { FeedbackModal } from "./feedback-modal"

interface StudentDetailsTabsProps {
  studentDetails: any
  onClose: () => void
}

export const StudentDetailsTabs: React.FC<StudentDetailsTabsProps> = ({ 
  studentDetails, 
  onClose 
}) => {
  const [feedbackText, setFeedbackText] = useState("")
  const [selectedLogId, setSelectedLogId] = useState<string | null>(null)
  const [finalReportFeedback, setFinalReportFeedback] = useState({
    supervisor_comments: "",
    supervisor_rating: "",
    grade: ""
  })

  // Feedback modal state
  const [feedbackModal, setFeedbackModal] = useState<{
    isOpen: boolean
    type: "daily_log" | "final_report"
    itemId: string
    title: string
  }>({
    isOpen: false,
    type: "daily_log",
    itemId: "",
    title: ""
  })

  // Hooks
  const addDailyLogFeedback = useAddDailyLogFeedback()
  const addFinalReportFeedback = useAddFinalReportFeedback()

  const { student, daily_log_entries = [], weekly_reports = [], final_report, feedback = [] } = studentDetails

  // Open feedback modal
  const openFeedbackModal = (type: "daily_log" | "final_report", itemId: string, title: string) => {
    setFeedbackModal({
      isOpen: true,
      type,
      itemId,
      title
    })
  }

  // Close feedback modal
  const closeFeedbackModal = () => {
    setFeedbackModal(prev => ({
      ...prev,
      isOpen: false
    }))
  }

  // Handle daily log feedback submission
  const handleDailyLogFeedback = async () => {
    if (!selectedLogId || !feedbackText.trim()) return

    try {
      await addDailyLogFeedback.mutateAsync({
        daily_log_id: selectedLogId,
        supervisor_feedback: feedbackText.trim()
      })
      setFeedbackText("")
      setSelectedLogId(null)
    } catch (error) {
      console.error('Failed to add daily log feedback:', error)
    }
  }

  // Handle final report feedback submission
  const handleFinalReportFeedback = async () => {
    if (!final_report?.id) return

    try {
      await addFinalReportFeedback.mutateAsync({
        final_report_id: final_report.id,
        feedback: {
          supervisor_comments: finalReportFeedback.supervisor_comments,
          supervisor_rating: finalReportFeedback.supervisor_rating ? parseInt(finalReportFeedback.supervisor_rating) : undefined,
          grade: finalReportFeedback.grade || undefined
        }
      })
      setFinalReportFeedback({
        supervisor_comments: "",
        supervisor_rating: "",
        grade: ""
      })
    } catch (error) {
      console.error('Failed to add final report feedback:', error)
    }
  }

  return (
    <div className="w-full">
      {/* Student Info Header */}
      {/* <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            {student?.student_name}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{student?.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{student?.phone || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{student?.program_name || 'N/A'}</span>
            </div>
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm">{student?.class || 'N/A'}</span>
            </div>
          </div>
        </CardContent>
      </Card> */}

      {/* Tabs */}
      <Tabs defaultValue="daily-logs" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="daily-logs" className="flex items-center gap-2">
            <BookOpen className="h-4 w-4" />
            Daily Logs
          </TabsTrigger>
          <TabsTrigger value="weekly-reports" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Weekly Reports
          </TabsTrigger>
          <TabsTrigger value="final-report" className="flex items-center gap-2">
            <Star className="h-4 w-4" />
            Final Report
          </TabsTrigger>
          <TabsTrigger value="feedback" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Feedback
          </TabsTrigger>
        </TabsList>

        {/* Daily Logs Tab */}
        <TabsContent value="daily-logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Daily Log Entries</CardTitle>
              <CardDescription>
                Review and provide feedback on student's daily activities
              </CardDescription>
            </CardHeader>
            <CardContent>
              {daily_log_entries.length === 0 ? (
                <div className="text-center py-8">
                  <BookOpen className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No daily log entries found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {daily_log_entries.map((log: any) => (
                    <Card key={log.id} className="border-l-4 border-l-blue-500">
                      <CardHeader className="pb-3">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{log.title}</CardTitle>
                          <div className="flex items-center gap-2">
                            <Badge variant={log.status === 'submitted' ? 'default' : 'secondary'}>
                              {log.status}
                            </Badge>
                            <div className="flex items-center gap-1 text-sm text-muted-foreground">
                              <Calendar className="h-4 w-4" />
                              {new Date(log.date).toLocaleDateString()}
                            </div>
                          </div>
                        </div>
                      </CardHeader>
                      <CardContent className="space-y-3">
                        <div>
                          <h4 className="font-medium mb-2">Activities</h4>
                          <p className="text-sm text-muted-foreground">{log.activities}</p>
                        </div>
                        
                        {log.tasks_completed && (
                          <div>
                            <h4 className="font-medium mb-2">Tasks Completed</h4>
                            <p className="text-sm text-muted-foreground">{log.tasks_completed}</p>
                          </div>
                        )}
                        
                        {log.challenges_faced && (
                          <div>
                            <h4 className="font-medium mb-2">Challenges Faced</h4>
                            <p className="text-sm text-muted-foreground">{log.challenges_faced}</p>
                          </div>
                        )}
                        
                        {log.skills_learned && (
                          <div>
                            <h4 className="font-medium mb-2">Skills Learned</h4>
                            <p className="text-sm text-muted-foreground">{log.skills_learned}</p>
                          </div>
                        )}

                        <div className="flex items-center gap-4 text-sm">
                          {log.hours_worked && (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4" />
                              {log.hours_worked} hours
                            </div>
                          )}
                          <div className="flex items-center gap-1">
                            <span>Supervisor Present:</span>
                            <Badge variant={log.supervisor_present ? 'default' : 'secondary'}>
                              {log.supervisor_present ? 'Yes' : 'No'}
                            </Badge>
                          </div>
                        </div>

                        {/* Existing Supervisor Feedback */}
                        {log.supervisor_feedback && (
                          <div className="bg-blue-50 p-3 rounded-lg border border-blue-200">
                            <h4 className="font-medium mb-2 text-blue-800">Your Feedback</h4>
                            <p className="text-sm text-blue-700">{log.supervisor_feedback}</p>
                          </div>
                        )}

                        {/* Action Buttons */}
                        <div className="flex justify-end pt-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => openFeedbackModal("daily_log", log.id, log.title)}
                            className="gap-2"
                          >
                            <MessageSquare className="h-4 w-4" />
                            {log.supervisor_feedback ? "Update Feedback" : "Add Feedback"}
                          </Button>
                        </div>

                        {/* Add Feedback Section */}
                        {selectedLogId === log.id ? (
                          <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
                            <Label htmlFor="feedback">Add Supervisor Feedback</Label>
                            <Textarea
                              id="feedback"
                              value={feedbackText}
                              onChange={(e) => setFeedbackText(e.target.value)}
                              placeholder="Provide feedback on this daily log entry..."
                              rows={3}
                            />
                            <div className="flex gap-2">
                              <Button 
                                onClick={handleDailyLogFeedback}
                                disabled={!feedbackText.trim() || addDailyLogFeedback.isPending}
                                size="sm"
                              >
                                {addDailyLogFeedback.isPending ? "Adding..." : "Add Feedback"}
                              </Button>
                              <Button 
                                variant="outline" 
                                onClick={() => {
                                  setSelectedLogId(null)
                                  setFeedbackText("")
                                }}
                                size="sm"
                              >
                                Cancel
                              </Button>
                            </div>
                          </div>
                        ) : (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => setSelectedLogId(log.id)}
                            className="w-fit"
                          >
                            {log.supervisor_feedback ? "Update Feedback" : "Add Feedback"}
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Weekly Reports Tab */}
        <TabsContent value="weekly-reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Weekly Reports</CardTitle>
              <CardDescription>
                Student's weekly progress reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              {weekly_reports.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No weekly reports found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {weekly_reports.map((report: any) => (
                    <Card key={report.id} className="border-l-4 border-l-green-500">
                      <CardHeader>
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">Week {report.week_number}</CardTitle>
                          <Badge variant={report.status === 'submitted' ? 'default' : 'secondary'}>
                            {report.status}
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="space-y-3">
                          <div>
                            <h4 className="font-medium mb-2">Summary</h4>
                            <p className="text-sm text-muted-foreground">{report.content || 'No content available'}</p>
                          </div>
                          {report.submitted_at && (
                            <div className="text-sm text-muted-foreground">
                              Submitted: {new Date(report.submitted_at).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Final Report Tab */}
        <TabsContent value="final-report" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Final Report</CardTitle>
              <CardDescription>
                Student's final internship report and your evaluation
              </CardDescription>
            </CardHeader>
            <CardContent>
              {!final_report ? (
                <div className="text-center py-8">
                  <Star className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No final report submitted yet</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Report Content */}
                  <div>
                    <h4 className="font-medium mb-3">Report Content</h4>
                    {final_report.content_html ? (
                      <div 
                        className="prose max-w-none text-sm border rounded-lg p-4 bg-gray-50"
                        dangerouslySetInnerHTML={{ __html: final_report.content_html }}
                      />
                    ) : final_report.file_url ? (
                      <div className="border rounded-lg p-4 bg-gray-50">
                        <p className="text-sm text-muted-foreground mb-2">Uploaded File:</p>
                        <a 
                          href={final_report.file_url} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-blue-600 hover:underline"
                        >
                          {final_report.file_name || 'Download Report'}
                        </a>
                      </div>
                    ) : (
                      <p className="text-sm text-muted-foreground">No content available</p>
                    )}
                  </div>

                  {/* Existing Supervisor Feedback */}
                  {final_report.supervisor_comments && (
                    <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                      <h4 className="font-medium mb-2 text-blue-800">Your Feedback</h4>
                      <p className="text-sm text-blue-700 mb-2">{final_report.supervisor_comments}</p>
                      {final_report.supervisor_rating && (
                        <div className="flex items-center gap-2">
                          <span className="text-sm font-medium">Rating:</span>
                          <Badge variant="default">{final_report.supervisor_rating}/5</Badge>
                        </div>
                      )}
                      {final_report.grade && (
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-sm font-medium">Grade:</span>
                          <Badge variant="default">{final_report.grade}</Badge>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Add/Update Feedback Form */}
                  <div className="bg-gray-50 p-4 rounded-lg space-y-4">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium">
                        {final_report.supervisor_comments ? "Update" : "Add"} Supervisor Feedback
                      </h4>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => openFeedbackModal("final_report", final_report.id, "Final Report")}
                        className="gap-2"
                      >
                        <MessageSquare className="h-4 w-4" />
                        Quick Feedback
                      </Button>
                    </div>
                    
                    <div className="space-y-3">
                      <div>
                        <Label htmlFor="supervisor-comments">Comments</Label>
                        <Textarea
                          id="supervisor-comments"
                          value={finalReportFeedback.supervisor_comments}
                          onChange={(e) => setFinalReportFeedback(prev => ({
                            ...prev,
                            supervisor_comments: e.target.value
                          }))}
                          placeholder="Provide detailed feedback on the final report..."
                          rows={4}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="rating">Rating (1-5)</Label>
                          <Select 
                            value={finalReportFeedback.supervisor_rating} 
                            onValueChange={(value) => setFinalReportFeedback(prev => ({
                              ...prev,
                              supervisor_rating: value
                            }))}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select rating" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="1">1 - Poor</SelectItem>
                              <SelectItem value="2">2 - Fair</SelectItem>
                              <SelectItem value="3">3 - Good</SelectItem>
                              <SelectItem value="4">4 - Very Good</SelectItem>
                              <SelectItem value="5">5 - Excellent</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>

                        <div>
                          <Label htmlFor="grade">Grade</Label>
                          <Input
                            id="grade"
                            value={finalReportFeedback.grade}
                            onChange={(e) => setFinalReportFeedback(prev => ({
                              ...prev,
                              grade: e.target.value
                            }))}
                            placeholder="e.g., A, B+, 85%"
                          />
                        </div>
                      </div>

                      <Button 
                        onClick={handleFinalReportFeedback}
                        disabled={!finalReportFeedback.supervisor_comments.trim() || addFinalReportFeedback.isPending}
                      >
                        {addFinalReportFeedback.isPending ? "Saving..." : "Save Feedback"}
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Feedback Tab */}
        <TabsContent value="feedback" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Feedback</CardTitle>
              <CardDescription>
                History of all feedback provided to this student
              </CardDescription>
            </CardHeader>
            <CardContent>
              {feedback.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No feedback history found</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {feedback.map((item: any) => (
                    <Card key={item.id} className="border-l-4 border-l-purple-500">
                      <CardContent className="pt-4">
                        <div className="space-y-2">
                          <div className="flex items-center justify-between">
                            <Badge variant="outline">{item.type || 'General'}</Badge>
                            <span className="text-sm text-muted-foreground">
                              {new Date(item.created_at).toLocaleDateString()}
                            </span>
                          </div>
                          <p className="text-sm">{item.content || item.comments}</p>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={feedbackModal.isOpen}
        onClose={closeFeedbackModal}
        type={feedbackModal.type}
        itemId={feedbackModal.itemId}
        studentName={student?.student_name || "Student"}
        title={feedbackModal.title}
      />
    </div>
  )
}
